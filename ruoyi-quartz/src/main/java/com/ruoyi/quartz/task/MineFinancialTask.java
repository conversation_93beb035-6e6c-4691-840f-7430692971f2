package com.ruoyi.quartz.task;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.bussiness.domain.*;
import com.ruoyi.bussiness.domain.setting.FinancialSettlementSetting;
import com.ruoyi.bussiness.domain.setting.Setting;
import com.ruoyi.bussiness.mapper.TMineFinancialMapper;
import com.ruoyi.bussiness.service.*;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.*;
import com.ruoyi.common.utils.DateUtil;
import com.ruoyi.common.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

@Component("mineFinancialTask")
@Slf4j
public class MineFinancialTask {


    @Resource
    private SettingService settingService;
    @Resource
    private TMineFinancialMapper tMineFinancialMapper;
    @Resource
    private ITAppUserService appUserService;
    @Resource
    private RedisCache redisCache;
    @Resource
    private ITAppAssetService tAppAssetService;
    @Resource
    private ITMineOrderService orderService;
    @Resource
    private ITMineUserService mineUserService;
    @Resource
    private ITAppWalletRecordService walletRecordService;
    @Resource
    private ITMineOrderDayService mineOrderDayService;


    /**
     * 到期结算 每日结算   指定收益入库
     */
    @Scheduled(cron = "0 */1 * * * ?")
    public void mineFinancialTask() {
        try {
            log.info("收益结算定时任务开始执行...");
            //查看系统配置  获取结算方式
            Setting setting = settingService.get(SettingEnum.FINANCIAL_SETTLEMENT_SETTING.name());
            FinancialSettlementSetting settlementSetting = JSONUtil.toBean(setting.getSettingValue(), FinancialSettlementSetting.class);
            //查看系统今日待下发收益的订单
            List<TMineOrder> list1 = orderService.list(new LambdaQueryWrapper<TMineOrder>().eq(TMineOrder::getStatus, 0L));
            log.error("收益结算定时任务{}", list1.toArray());
            if (CollectionUtils.isEmpty(list1)){
                return;
            }
            for (TMineOrder tMineOrder : list1) {
                settlement(tMineOrder,settlementSetting);
            }
        } catch (Exception e) {
            log.error("收益结算定时任务结算异常:{}", e.getMessage());
        }

    }

    private void settlement(TMineOrder order, FinancialSettlementSetting setting) {
        try {
            // 首先检查用户是否存在，不存在则直接跳过
            TAppUser appUser = appUserService.getById(order.getUserId());
            if (appUser == null) {
                log.error("找不到用户 {}, 跳过结算", order.getUserId());
                return;
            }

            BigDecimal amount = order.getAmount();

            //获取用户资产，如果用户存在但资产不存在，则创建资产记录
            TAppAsset asset = tAppAssetService.getAssetByUserIdAndType(order.getUserId(), AssetEnum.FINANCIAL_ASSETS.getCode());

            // 如果用户没有理财资产记录，创建一个
            if (asset == null) {
                log.info("用户 {} 没有理财资产记录，正在创建...", order.getUserId());
                try {
                    tAppAssetService.createAsset(appUser, "usdt", AssetEnum.FINANCIAL_ASSETS.getCode());
                    asset = tAppAssetService.getAssetByUserIdAndType(order.getUserId(), AssetEnum.FINANCIAL_ASSETS.getCode());
                    log.info("用户 {} 理财资产记录创建完成", order.getUserId());
                } catch (Exception e) {
                    log.error("为用户 {} 创建理财资产记录失败: {}", order.getUserId(), e.getMessage());
                    throw new RuntimeException("无法创建用户资产记录");
                }
            }

            // 最终安全检查
            if (asset == null) {
                log.error("无法为用户 {} 创建或获取理财资产记录，跳过结算", order.getUserId());
                return;
            }

            //获取利率  最大 最小 中间 随机
            BigDecimal dayRatio=getRatio(order.getMinOdds(),order.getMaxOdds());
            //获取对应产品的日收益
            BigDecimal earn = amount.multiply(dayRatio).divide(new BigDecimal(100)).setScale(6, RoundingMode.UP);
            //查看是否之前结算过
            TMineOrderDay mineOrderDay = mineOrderDayService.getOne(new LambdaQueryWrapper<TMineOrderDay>().eq(TMineOrderDay::getStatus,1).eq(TMineOrderDay::getOrderNo, order.getOrderNo()));
            if(mineOrderDay !=null ){
                mineOrderDay.setEarn(mineOrderDay.getEarn().add(earn));
            }else {
                mineOrderDay=new TMineOrderDay();
                //组装结算订单
                mineOrderDay.setAddress(order.getAdress() != null ? order.getAdress() : "");
                mineOrderDay.setOdds(dayRatio);
                mineOrderDay.setOrderNo(order.getOrderNo());
                mineOrderDay.setEarn(earn);
                mineOrderDay.setPlanId(order.getPlanId());
                mineOrderDay.setAmount(amount);
                mineOrderDay.setCreateTime(new Date());
                mineOrderDay.setUpdateTime(new Date());
                mineOrderDay.setType(0L);
                mineOrderDay.setCreateBy("system"); // 设置创建者
                mineOrderDay.setUpdateBy("system"); // 设置更新者
            }

            //判断 日结 、  指定日结 、订单到期结算
            if(Objects.equals(CommonEnum.ONE.getCode(), setting.getSettlementType())){
                // 指定日期：日常只累计到明细，不入账
                mineOrderDay.setStatus(CommonEnum.ONE.getCode());
                mineOrderDayService.saveOrUpdate(mineOrderDay);
            }
            if(Objects.equals(CommonEnum.TWO.getCode(), setting.getSettlementType())){
                //日结
                //最后一天结算本金+收益  否则只结算收益
                BigDecimal availableAmount = asset.getAvailableAmount();
                asset.setAmout(asset.getAmout().add(earn));
                asset.setAvailableAmount(asset.getAvailableAmount().add(earn));
                mineOrderDay.setStatus(CommonEnum.TWO.getCode());
                tAppAssetService.updateTAppAsset(asset);
                mineOrderDayService.saveOrUpdate(mineOrderDay);
                walletRecordService.generateRecord(order.getUserId(), earn, RecordEnum.FINANCIAL_SETTLEMENT.getCode(),"",order.getOrderNo(),RecordEnum.FINANCIAL_SETTLEMENT.getInfo(),availableAmount,asset.getAvailableAmount(),asset.getSymbol(),appUser.getAdminParentIds());
            }
            if(Objects.equals(CommonEnum.THREE.getCode(), setting.getSettlementType())){
                // 到期结算：到期时一次性返还本息
                if(DateUtil.daysBetween(order.getEndTime(),new Date())>=0){
                    mineOrderDay.setStatus(CommonEnum.TWO.getCode());
                    mineOrderDayService.saveOrUpdate(mineOrderDay);
                    BigDecimal earn1 = mineOrderDay.getEarn();
                    // 到期结算：本金 + 收益一起返还
                    BigDecimal totalAmount = order.getAmount().add(earn1);
                    BigDecimal availableAmount = asset.getAvailableAmount();
                    asset.setAmout(asset.getAmout().add(totalAmount));
                    asset.setAvailableAmount(asset.getAvailableAmount().add(totalAmount));
                    tAppAssetService.updateTAppAsset(asset);
                    // 记录本金+收益的返还
                    walletRecordService.generateRecord(order.getUserId(), totalAmount, RecordEnum.FINANCIAL_SETTLEMENT.getCode(),"",order.getOrderNo(),RecordEnum.FINANCIAL_SETTLEMENT.getInfo(),availableAmount,asset.getAvailableAmount(),asset.getSymbol(),appUser.getAdminParentIds());
                    log.info("理财产品到期结算完成 - 订单:{}, 本金:{}, 收益:{}, 总返还:{}", order.getOrderNo(), order.getAmount(), earn1, totalAmount);
                    //返利
                    //itActivityMineService.caseBackToFather(wallet.getUserId(), m.getAccumulaEarn(), wallet.getUserName(), orderNo);
                }else {
                    mineOrderDay.setStatus(CommonEnum.ONE.getCode());
                    mineOrderDayService.saveOrUpdate(mineOrderDay);
                }
            }

            // 安全检查收益金额
            BigDecimal earnAmount = mineOrderDay.getEarn();
            if (earnAmount == null) {
                earnAmount = BigDecimal.ZERO;
                log.warn("订单 {} 的收益金额为null，设置为0", order.getOrderNo());
            }
            order.setAccumulaEarn(earnAmount);
            //判断 是否产品到期
            int daysDiff = DateUtil.daysBetween(order.getEndTime(),new Date());
            log.info("订单 {} 到期时间: {}, 当前时间: {}, 天数差: {}",
                order.getOrderNo(), order.getEndTime(), new Date(), daysDiff);
            if(daysDiff >= 0){
                log.info("订单 {} 已过期，更新状态为已结算", order.getOrderNo());

                // 订单到期时，如果是日结模式，需要返还本金
                if(Objects.equals(CommonEnum.TWO.getCode(), setting.getSettlementType())){
                    BigDecimal principal = order.getAmount();
                    BigDecimal availableAmount = asset.getAvailableAmount();
                    asset.setAmout(asset.getAmout().add(principal));
                    asset.setAvailableAmount(asset.getAvailableAmount().add(principal));
                    tAppAssetService.updateTAppAsset(asset);
                    // 记录本金返还
                    walletRecordService.generateRecord(order.getUserId(), principal, RecordEnum.FINANCIAL_SETTLEMENT.getCode(),"",order.getOrderNo(),"理财本金返还",availableAmount,asset.getAvailableAmount(),asset.getSymbol(),appUser.getAdminParentIds());
                    log.info("日结模式订单到期，返还本金 - 订单:{}, 本金:{}", order.getOrderNo(), principal);
                }

                order.setStatus(1L);
                order.setSettleTime(new Date()); // 设置结算时间
            } else {
                log.info("订单 {} 尚未过期，保持收益中状态", order.getOrderNo());
            }
            int updateResult = orderService.updateTMineOrder(order);
            log.info("订单 {} 更新结果: {}, 当前状态: {}", order.getOrderNo(), updateResult > 0 ? "成功" : "失败", order.getStatus());
        } catch (Exception e) {
            log.error("理财订单异常结算, MinderOrder:{}, 异常信息: {}", order, e.getMessage(), e);
            // 对于批量处理，不重新抛出异常，避免中断其他订单的处理
            // 对于单个订单处理，会在调用方法中处理异常
            throw e;
        }
    }


    //获取订单的利率
    private BigDecimal getRatio(BigDecimal minOdds, BigDecimal maxOdds) {
        return queryHongBao(minOdds.doubleValue(), maxOdds.doubleValue());
    }

    private static BigDecimal queryHongBao(double min, double max) {
        Random rand = new Random();
        double result;
        result = min + (rand.nextDouble() * (max - min));
        return new BigDecimal(result).setScale(4, RoundingMode.UP);
    }

    /**
     * 手动结算过期订单 - 处理历史遗留的过期未结算订单
     */
    @Transactional
    public void manualSettlementExpiredOrders() {
        try {
            log.info("开始手动结算过期订单...");
            //查看系统配置  获取结算方式
            Setting setting = settingService.get(SettingEnum.FINANCIAL_SETTLEMENT_SETTING.name());
            FinancialSettlementSetting settlementSetting = JSONUtil.toBean(setting.getSettingValue(), FinancialSettlementSetting.class);

            //查找所有已过期但未结算的订单 (status=0 且 endTime < 当前时间)
            List<TMineOrder> expiredOrders = orderService.list(new LambdaQueryWrapper<TMineOrder>()
                    .eq(TMineOrder::getStatus, 0L)
                    .lt(TMineOrder::getEndTime, new Date()));

            if (CollectionUtils.isEmpty(expiredOrders)){
                log.info("没有找到过期未结算的订单");
                return;
            }

            log.info("找到 {} 个过期未结算的订单", expiredOrders.size());
            int successCount = 0;
            int failCount = 0;

            for (TMineOrder expiredOrder : expiredOrders) {
                try {
                    log.info("处理过期订单: {}, 到期时间: {}", expiredOrder.getOrderNo(), expiredOrder.getEndTime());
                    settlement(expiredOrder, settlementSetting);
                    successCount++;
                    log.info("订单 {} 结算成功", expiredOrder.getOrderNo());
                } catch (Exception e) {
                    failCount++;
                    log.error("订单 {} 结算失败: {}", expiredOrder.getOrderNo(), e.getMessage(), e);
                    // 继续处理下一个订单，不中断整个批量处理
                }
            }

            log.info("手动结算过期订单完成，成功: {} 个，失败: {} 个", successCount, failCount);
            log.info("手动结算过期订单完成");
        } catch (Exception e) {
            log.error("手动结算过期订单异常", e);
        }
    }

    /**
     * 手动结算单个订单
     */
    @Transactional
    public void manualSettlementSingleOrder(Long orderId) {
        try {
            log.info("开始手动结算单个订单，订单ID: {}", orderId);

            // 获取结算配置
            Setting setting = settingService.get(SettingEnum.FINANCIAL_SETTLEMENT_SETTING.name());
            FinancialSettlementSetting settlementSetting = JSONUtil.toBean(setting.getSettingValue(), FinancialSettlementSetting.class);

            // 获取指定订单
            TMineOrder order = orderService.getById(orderId);
            if (order == null) {
                log.error("订单不存在，订单ID: {}", orderId);
                throw new RuntimeException("订单不存在");
            }

            if (order.getStatus() != 0) {
                log.error("订单状态不是收益中，无法结算，订单ID: {}, 当前状态: {}", orderId, order.getStatus());
                throw new RuntimeException("订单状态不是收益中，无法结算");
            }

            log.info("开始结算订单: {}, 到期时间: {}", order.getOrderNo(), order.getEndTime());
            settlement(order, settlementSetting);
            log.info("单个订单结算完成，订单ID: {}", orderId);

        } catch (Exception e) {
            log.error("单个订单结算失败，订单ID: {}, 异常信息: {}", orderId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 简化的手动结算方法 - 直接更新过期订单状态
     */
    @Transactional
    public void simpleManualSettlement() {
        try {
            log.info("开始简化手动结算过期订单...");

            //查找所有已过期但未结算的订单
            List<TMineOrder> expiredOrders = orderService.list(new LambdaQueryWrapper<TMineOrder>()
                    .eq(TMineOrder::getStatus, 0L)
                    .lt(TMineOrder::getEndTime, new Date()));

            if (CollectionUtils.isEmpty(expiredOrders)){
                log.info("没有找到过期未结算的订单");
                return;
            }

            log.info("找到 {} 个过期未结算的订单", expiredOrders.size());
            int successCount = 0;
            int failCount = 0;

            for (TMineOrder expiredOrder : expiredOrders) {
                try {
                    log.info("处理过期订单: {}, 到期时间: {}, 当前状态: {}",
                        expiredOrder.getOrderNo(), expiredOrder.getEndTime(), expiredOrder.getStatus());

                    // 直接更新状态为已结算
                    expiredOrder.setStatus(1L);
                    expiredOrder.setSettleTime(new Date());

                    int updateResult = orderService.updateTMineOrder(expiredOrder);
                    if (updateResult > 0) {
                        successCount++;
                        log.info("订单 {} 状态更新成功", expiredOrder.getOrderNo());
                    } else {
                        failCount++;
                        log.error("订单 {} 状态更新失败，没有行被更新", expiredOrder.getOrderNo());
                    }
                } catch (Exception e) {
                    failCount++;
                    log.error("订单 {} 状态更新异常: {}", expiredOrder.getOrderNo(), e.getMessage(), e);
                }
            }

            log.info("简化手动结算完成，成功: {} 个，失败: {} 个", successCount, failCount);
            log.info("简化手动结算完成");
        } catch (Exception e) {
            log.error("简化手动结算异常", e);
            throw e;
        }
    }

    /**
     * 指定日期结算
     */
    @Scheduled(cron = "0 0 0 * * ?")
    public void  specifiedDateSettlement(){
        //查看系统配置  获取结算方式
        Setting setting = settingService.get(SettingEnum.FINANCIAL_SETTLEMENT_SETTING.name());
        FinancialSettlementSetting settlementSetting = JSONUtil.toBean(setting.getSettingValue(), FinancialSettlementSetting.class);
        if(CommonEnum.ONE.getCode().equals(settlementSetting.getSettlementType())){
            // 查找未结算的收益
            List<TMineOrderDay> list = mineOrderDayService.list(new LambdaQueryWrapper<TMineOrderDay>().eq(TMineOrderDay::getStatus, CommonEnum.ONE.getCode()));
            if(CollectionUtils.isEmpty(list)){
                return;
            }
            for (TMineOrderDay mineOrderDay : list) {
                String orderNo = mineOrderDay.getOrderNo();
                //查找订单
                TMineOrder order = orderService.getOne(new LambdaQueryWrapper<TMineOrder>().eq(TMineOrder::getOrderNo, orderNo));
                TAppUser appUser = appUserService.getById(order.getUserId());
                if(CommonEnum.ONE.getCode().equals(order.getStatus().intValue())){
                    TAppAsset asset = tAppAssetService.getAssetByUserIdAndType(order.getUserId(), AssetEnum.FINANCIAL_ASSETS.getCode());
                    mineOrderDay.setStatus(CommonEnum.TWO.getCode());
                    mineOrderDayService.saveOrUpdate(mineOrderDay);
                    BigDecimal earn = mineOrderDay.getEarn();
                    BigDecimal availableAmount = asset.getAvailableAmount();
                    asset.setAmout(asset.getAmout().add(earn));
                    asset.setAvailableAmount(asset.getAvailableAmount().add(earn));
                    tAppAssetService.updateTAppAsset(asset);
                    walletRecordService.generateRecord(order.getUserId(), earn, RecordEnum.FINANCIAL_SETTLEMENT.getCode(),"",order.getOrderNo(),RecordEnum.FINANCIAL_SETTLEMENT.getInfo(),availableAmount,asset.getAvailableAmount(),asset.getSymbol(),appUser.getAdminParentIds());
                    //返利
                    //itActivityMineService.caseBackToFather(wallet.getUserId(), m.getAccumulaEarn(), wallet.getUserName(), orderNo);
                }
            }
        }

    }
}
