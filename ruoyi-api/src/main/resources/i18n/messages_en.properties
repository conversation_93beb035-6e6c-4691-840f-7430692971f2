user.appname=\u82F1\u6587



#\u9519\u8BEF\u6D88\u606F
not.null= required
user.jcaptcha.error=Verification code error
user.jcaptcha.expire=Verification code has expired
user.not.exists=User does not exist/wrong password
user.password.not.match=User does not exist/wrong password
user.password.retry.limit.count=The wrong password was entered {0} times
user.password.retry.limit.exceed=The password was entered incorrectly {0} times, and the account was locked for {1} minutes
user.password.delete=Sorry, your account has been deleted
user.blocked=The user has been banned, please contact the administrator
role.blocked=The role has been banned, please contact the administrator
login.blocked=Unfortunately, the access IP has been blacklisted by the system
user.logout.success=exit successfully
user.ops.status=Successful operation, Please wait

length.not.valid=Length must be between {min} and {max} characters

user.username.not.valid=* 2 to 20 Chinese characters, letters, numbers or underscores, and must start with a non-number
user.password.not.valid=* 5-50 characters

user.email.not.valid=E-mail format error
user.mobile.phone.number.not.valid=Malformed phone number
user.login.success=login successful
user.register.success=registration success
user.notfound=please login again
user.forcelogout=The administrator forcibly logged out, please log in again
user.unknown.error=Unknown error, please log in again

##\u6587\u4EF6\u4E0A\u4F20\u6D88\u606F
upload.exceed.maxSize=The uploaded file size exceeds the file size limit!  The maximum allowed file size is: {0}MB!
upload.filename.exceed.length=The maximum length of the uploaded file name is {0} characters

##\u6743\u9650
no.permission=\u60A8\u6CA1\u6709\u6570\u636E\u7684\u6743\u9650\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\u6DFB\u52A0\u6743\u9650 [{0}]
no.create.permission=\u60A8\u6CA1\u6709\u521B\u5EFA\u6570\u636E\u7684\u6743\u9650\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\u6DFB\u52A0\u6743\u9650 [{0}]
no.update.permission=\u60A8\u6CA1\u6709\u4FEE\u6539\u6570\u636E\u7684\u6743\u9650\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\u6DFB\u52A0\u6743\u9650 [{0}]
no.delete.permission=\u60A8\u6CA1\u6709\u5220\u9664\u6570\u636E\u7684\u6743\u9650\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\u6DFB\u52A0\u6743\u9650 [{0}]
no.export.permission=\u60A8\u6CA1\u6709\u5BFC\u51FA\u6570\u636E\u7684\u6743\u9650\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\u6DFB\u52A0\u6743\u9650 [{0}]
no.view.permission=\u60A8\u6CA1\u6709\u67E5\u770B\u6570\u636E\u7684\u6743\u9650\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\u6DFB\u52A0\u6743\u9650 [{0}]

##\u6CE8\u518C\u7528\u6237\u63D0\u793A
user.register.email.format=E-mail format is incorrect
user.register.email.exisit=mailbox already exists
user.register.phone.exisit=mobile number already exists
user.user_name_exisit=username already exists
login.user_error=wrong user name or password
user.login.address.error=Address is taken!
user.register.phone.exist=Mobile phone number already exists
user.register.phone.bind=Mobile phone number has been bound
#app
app.login.address.not.null=user address is empty
login.email.not_register=Please use the bound email address to operate
login.phone.not_register=Please use the bound mobile phone number to operate
login.code_error=Verification code error
user.login.code.error=Verification code validation failed!
user.login.password.null=Please enter your password!
user.login.upd.success=Successfully modified!
phone_code_empty=Mobile phone number cannot be empty!
email.code_empty=E-mail can not be empty!
user.code.send=Verification code sent successfully
app.verification.email.code=The verification code has been sent to your email, please check it
user.password_bind=The password has already been set, please do not bind again
user.tard.password_bind=The security password has been set, please do not bind again
user.login.null=User does not exist!
user.login.old.password=Old password not entered!
user.login.new.password=New password not entered!
user.login.paw.upd=The new password cannot be the same as the old password!
user.login.old.password.error=The old password is wrong!
user.login.address.null=Address is empty!
user.login.userid.null=User ID is empty!
#\u63D0\u73B0
user.password_notbind=Please set a secure password
tard_password.error=Wrong payment password
withdraw.amount_number_exceed=The number of cash withdrawals on the day exceeds the set number
withdraw_error= Insufficient balance, unable to withdraw cash
withdraw.amount_error=The withdrawal amount is wrong, please modify
withdraw.refresh=please refresh
withdraw.address.isnull=Please fill in the correct withdrawal address!
withdraw_require_error = At present, I still need to play with the remaining turnover.
withdraw_error_coin=This currency does not exist
withdraw_error_rate=This currency exchange rate does not exist
#\u5B9E\u540D\u8BA4\u8BC1
user.kyc.not_blank=All 4 items of identity authentication information are required

#\u5151\u6362
exchange.symbol.exist = The exchange currency cannot be empty, and the exchange amount must be greater than 0
recharge.amout.min=The minimum recharge amount is {0}
recharge.amout.max=The maximum recharge amount is {0}
currency.exchange_min = The minimum exchange amount is {0}
currency.exchange_max = The maximum exchange amount is {0}
exchange_error=The exchange amount is greater than the account balance
exchange.record.exist.error=You have a redemption in progress, try again later
#\u79D2\u5408\u7EA6
order_amount_error=Insufficient wallet balance, unable to purchase
order_10s_retry=Orders are placed too frequently, please try to place an order after 10s...
#\u7406\u8D22
user.push.message=User action prohibited
mine.level.error=Your VIP level is not enough to purchase secondary wealth management products
order.single.min=The purchase amount is less than the minimum amount and cannot be purchased
order.single.max=The purchased amount is greater than the maximum limit and cannot be purchased
order.buy.insufficient=You can currently purchase up to {0}
product.removed=The product has been discontinued
financial.count.max=This user has reached the limit and is not allowed to purchase
days.not.null=Buy cycle is empty
days.not.error=Purchase cycle error
contract.accont.error=Insufficient contract account balance
contract.min.share=The minimum purchase quantity is {0}
contract.max.share=The maximum number of purchases is {0}
contract.asset.error=Insufficient contract account balance
adjust.min.error=The reduced margin cannot be less than the initial margin
order.status.error=illegal submission
contract.buy.earn.error=Buy long, the take profit price cannot be less than the average opening price
contract.buy.loss.error=Buy long, the stop loss price cannot be greater than the average opening price
contract.sell.earn.error=Selling to open a short position, the take profit price cannot be greater than the average opening price
contract.sell.loss.error=Selling to open a short position, the stop loss price cannot be lower than the average opening price
contract.num.limit=The quantity cannot be greater than the open interest
#\u8D44\u91D1\u5212\u8F6C
asset_amount_error=Insufficient balance
order.amount_error=Insufficient wealth management account balance
order_amount_limit=Insufficient amount
order.10s_retry=\u4E0B\u5355\u8FC7\u4E8E\u9891\u7E41\uFF0C\u8BF710s\u540E\u5C1D\u8BD5\u4E0B\u5355...

contract.delivery.day=There are still: {0} days until delivery.
contract.delivery.margan=There are still: {0} days before delivery; if your  total contract is greater than or equal to {1} USDT or your  total contract is less than or equal to {2} USDT, you can close the position in advance.
contract.delivery.earn=There are still {0} days left before the delivery time; if your total contract assets is greater than or equal to {1} USDT, you can close the position in advance.
contract.delivery.loss=There are still: {0} days before the delivery time; if your total contract assets   is less than or equal to {1}USDT, you can close the position in advance.
order.audit.error=Under review
order.audit.pass=Recharging is in progress and requires confirmation from 5 nodes around the world. Please wait patiently.

#\u7533\u8D2D
own.coin.limit.num=Available quantity {0}
own.coin.limit=Available Balance {0}
own.coin.success=Subscription successful
own.coin.error=System error, please contact the administrator
own.coin.sub.play=You have already subscribed, please do not submit again!
own.coin.sub.success=You have obtained {0} subscription qualifications
own.coin.sub.error=Subscription is not available if you have not successfully subscribed
own.coin.sub.num.error=Please fill in the correct quantity
order.sell.min.error=The minimum sell amount is {0}
order.audit.reject=Failed to close position

#\u65B0\u53D1\u5E01\u8BA2\u9605
own.coin.subscribe.success=Application Successfully
own.coin.subscribe.error=Application failed
withdraw.kyc.error=Please complete real-name authentication first to lift the limit
exchange_symbol_error_exist=The exchange currency does not exist
currency.coin.setting.nonexistent=The current {} configuration does not exist
currency.balance.deficiency=The current {} currency balance is insufficient
currency.deal.error=This currency is not tradable
back.code.exist.error=Bank card number already exists{}

#\u5B9E\u540D\u8BA4\u8BC1
user.authentication.not.certified = Please perform primary certification first

#\u9ED1\u540D\u5355
user_is_black = Your account has been blacklisted and cannot be logged in.

#\u5E01\u5E01\u4EA4\u6613
currency.order.min.error= The minimum order quantity is {0}
currency.order.max.error= The maximum order quantity is {0}

