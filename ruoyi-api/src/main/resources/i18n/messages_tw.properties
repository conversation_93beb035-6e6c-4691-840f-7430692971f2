user.appname=\u4E2D\u6587\u7E41\u4F53



#\u9519\u8BEF\u6D88\u606F
not.null=* \u5FC5\u9808\u586B\u5BEB
user.jcaptcha.error=\u9A57\u8B49\u78BC\u932F\u8AA4
user.jcaptcha.expire=\u9A57\u8B49\u78BC\u5DF2\u5931\u6548
user.not.exists=\u7528\u6236\u4E0D\u5B58\u5728/\u5BC6\u78BC\u932F\u8AA4
user.password.not.match=\u7528\u6236\u4E0D\u5B58\u5728/\u5BC6\u78BC\u932F\u8AA4
user.password.retry.limit.count=\u5BC6\u78BC\u8F38\u5165\u932F\u8AA4{0}\u6B21
user.password.retry.limit.exceed=\u5BC6\u78BC\u8F38\u5165\u932F\u8AA4{0}\u6B21\uFF0C\u5E33\u6236\u9396\u5B9A{1}\u5206\u9418
user.password.delete=\u5C0D\u4E0D\u8D77\uFF0C\u60A8\u7684\u8CEC\u865F\u5DF2\u88AB\u522A\u9664
user.blocked=\u7528\u6236\u5DF2\u5C01\u7981\uFF0C\u8ACB\u806F\u7E6B\u7BA1\u7406\u54E1
role.blocked=\u89D2\u8272\u5DF2\u5C01\u7981\uFF0C\u8ACB\u806F\u7E6B\u7BA1\u7406\u54E1
login.blocked=\u5F88\u907A\u61BE\uFF0C\u8A2A\u554FIP\u5DF2\u88AB\u5217\u5165\u7CFB\u7D71\u9ED1\u540D\u55AE
user.logout.success=\u9000\u51FA\u6210\u529F
user.ops.status=\u64CD\u4F5C\u6210\u529F

length.not.valid=\u9577\u5EA6\u5FC5\u9808\u5728{min}\u5230{max}\u500B\u5B57\u7B26\u4E4B\u9593

user.username.not.valid=* 2\u523020\u500B\u6F22\u5B57\u3001\u5B57\u6BCD\u3001\u6578\u5B57\u6216\u4E0B\u5283\u7DDA\u7D44\u6210\uFF0C\u4E14\u5FC5\u9808\u4EE5\u975E\u6578\u5B57\u958B\u982D
user.password.not.valid=* 5-50\u500B\u5B57\u7B26

user.email.not.valid=\u90F5\u7BB1\u683C\u5F0F\u932F\u8AA4
user.mobile.phone.number.not.valid=\u624B\u6A5F\u865F\u683C\u5F0F\u932F\u8AA4
user.login.success=\u767B\u9304\u6210\u529F
user.register.success=\u8A3B\u518A\u6210\u529F
user.notfound=\u8ACB\u91CD\u65B0\u767B\u9304
user.forcelogout=\u7BA1\u7406\u54E1\u5F37\u5236\u9000\u51FA\uFF0C\u8ACB\u91CD\u65B0\u767B\u9304
user.unknown.error=\u672A\u77E5\u932F\u8AA4\uFF0C\u8ACB\u91CD\u65B0\u767B\u9304

##\u6587\u4EF6\u4E0A\u4F20\u6D88\u606F
upload.exceed.maxSize=\u4E0A\u4F20\u7684\u6587\u4EF6\u5927\u5C0F\u8D85\u51FA\u9650\u5236\u7684\u6587\u4EF6\u5927\u5C0F\uFF01<br/>\u5141\u8BB8\u7684\u6587\u4EF6\u6700\u5927\u5927\u5C0F\u662F\uFF1A{0}MB!
upload.filename.exceed.length=\u4E0A\u50B3\u7684\u6587\u4EF6\u540D\u6700\u9577{0}\u500B\u5B57\u7B26

##\u6743\u9650
no.permission=\u60A8\u6C92\u6709\u6578\u64DA\u7684\u6B0A\u9650\uFF0C\u8ACB\u806F\u7E6B\u7BA1\u7406\u54E1\u6DFB\u52A0\u6B0A\u9650 [{0}]
no.create.permission=\u60A8\u6C92\u6709\u5275\u5EFA\u6578\u64DA\u7684\u6B0A\u9650\uFF0C\u8ACB\u806F\u7E6B\u7BA1\u7406\u54E1\u6DFB\u52A0\u6B0A\u9650 [{0}]
no.update.permission=\u60A8\u6C92\u6709\u4FEE\u6539\u6578\u64DA\u7684\u6B0A\u9650\uFF0C\u8ACB\u806F\u7E6B\u7BA1\u7406\u54E1\u6DFB\u52A0\u6B0A\u9650 [{0}]
no.delete.permission=\u60A8\u6C92\u6709\u522A\u9664\u6578\u64DA\u7684\u6B0A\u9650\uFF0C\u8ACB\u806F\u7E6B\u7BA1\u7406\u54E1\u6DFB\u52A0\u6B0A\u9650 [{0}]
no.export.permission=\u60A8\u6C92\u6709\u5C0E\u51FA\u6578\u64DA\u7684\u6B0A\u9650\uFF0C\u8ACB\u806F\u7E6B\u7BA1\u7406\u54E1\u6DFB\u52A0\u6B0A\u9650 [{0}]
no.view.permission=\u60A8\u6C92\u6709\u67E5\u770B\u6578\u64DA\u7684\u6B0A\u9650\uFF0C\u8ACB\u806F\u7E6B\u7BA1\u7406\u54E1\u6DFB\u52A0\u6B0A\u9650 [{0}]

##\u6CE8\u518C\u7528\u6237\u63D0\u793A
user.register.email.format=\u90F5\u7BB1\u683C\u5F0F\u4E0D\u6B63\u78BA
user.register.email.exisit=\u90F5\u7BB1\u5DF2\u5B58\u5728
user.register.phone.exisit=\u624B\u6A5F\u865F\u5DF2\u5B58\u5728
user.user_name_exisit=\u7528\u6236\u540D\u5DF2\u7D93\u5B58\u5728
login.user_error=\u7528\u6236\u540D\u6216\u5BC6\u78BC\u932F\u8AA4
user.login.address.error=\u5730\u5740\u88AB\u4F54\u7528!
user.register.phone.exist=\u624B\u6A5F\u865F\u78BC\u5DF2\u7D93\u5B58\u5728
user.register.phone.bind=\u624B\u6A5F\u865F\u78BC\u5DF2\u7D93\u7D81\u5B9A
#app
app.login.address.not.null=\u7528\u6236\u5730\u5740\u70BA\u7A7A
login.email.not_register=\u8ACB\u4F7F\u7528\u5DF2\u7D81\u5B9A\u90F5\u7BB1\u9032\u884C\u64CD\u4F5C
login.phone.not_register=\u8ACB\u4F7F\u7528\u5DF2\u7D81\u5B9A\u624B\u6A5F\u865F\u9032\u884C\u64CD\u4F5C
login.code_error=\u9A57\u8B49\u78BC\u932F\u8AA4
user.login.code.error=\u9A57\u8B49\u78BC\u6548\u9A57\u5931\u6557!
user.login.password.null=\u8ACB\u8F38\u5165\u5BC6\u78BC!
user.login.upd.success=\u4FEE\u6539\u6210\u529F!
phone_code_empty=\u624B\u6A5F\u865F\u4E0D\u80FD\u70BA\u7A7A!
email.code_empty=\u90F5\u7BB1\u4E0D\u80FD\u70BA\u7A7A!
user.code.send=\u9A57\u8B49\u78BC\u767C\u9001\u6210\u529F
app.verification.email.code=\u9A57\u8B49\u78BC\u5DF2\u767C\u9001\u5230\u60A8\u7684\u90F5\u7BB1\uFF0C\u8ACB\u6CE8\u610F\u67E5\u6536
user.password_bind=\u5BC6\u78BC\u5DF2\u7D93\u8A2D\u7F6E\uFF0C\u8ACB\u52FF\u91CD\u8907\u7D81\u5B9A
user.tard.password_bind=\u5B89\u5168\u5BC6\u78BC\u5DF2\u7D93\u8A2D\u7F6E\uFF0C\u8ACB\u52FF\u91CD\u8907\u7D81\u5B9A
user.login.null=\u7528\u6236\u4E0D\u5B58\u5728\uFF01
user.login.old.password=\u820A\u5BC6\u78BC\u672A\u8F38\u5165\uFF01
user.login.new.password=\u65B0\u5BC6\u78BC\u672A\u8F38\u5165\uFF01
user.login.paw.upd=\u65B0\u5BC6\u78BC\u4E0D\u80FD\u8207\u820A\u5BC6\u78BC\u4E00\u81F4\uFF01
    user.login.old.password.error=\u820A\u5BC6\u78BC\u932F\u8AA4\uFF01
user.login.address.null=\u5730\u5740\u70BA\u7A7A\uFF01
user.login.userid.null=\u7528\u6236ID\u70BA\u7A7A\uFF01
#\u63D0\u73B0
user.password_notbind=\u8ACB\u8A2D\u7F6E\u5B89\u5168\u5BC6\u78BC
tard_password.error=\u652F\u4ED8\u5BC6\u78BC\u932F\u8AA4
withdraw.amount_number_exceed=\u7576\u65E5\u63D0\u73FE\u8D85\u904E\u8A2D\u5B9A\u6B21\u6578
withdraw_error=\u9918\u984D\u4E0D\u8DB3\uFF0C\u7121\u6CD5\u63D0\u73FE
withdraw.amount_error=\u63D0\u73FE\u91D1\u984D\u932F\u8AA4\uFF0C\u8ACB\u4FEE\u6539
withdraw.refresh=\u8ACB\u5237\u65B0
withdraw.address.isnull=\u8ACB\u586B\u5BEB\u6B63\u78BA\u7684\u9AD4\u73FE\u5730\u5740\uFF01
withdraw_require_error = \u76EE\u524D\u9084\u9700\u8981\u73A9\u5269\u4E0B\u7684\u6D41\u6C34
withdraw_error_coin=\u6B64\u5E63\u7A2E\u4E0D\u5B58\u5728
withdraw_error_rate=\u6B64\u5E63\u7A2E\u532F\u7387\u4E0D\u5B58\u5728
#\u5B9E\u540D\u8BA4\u8BC1
user.kyc.not_blank=\u8EAB\u4EFD\u8BA4\u8BC1\u4FE1\u606F4\u9879\u90FD\u662F\u5FC5\u586B

#\u5151\u6362
exchange.symbol.exist = \u514C\u63DB\u7684\u5E63\u7A2E\u4E0D\u80FD\u70BA\u7A7A,\u514C\u63DB\u91D1\u984D\u5FC5\u9808\u5927\u65BC0
recharge.amout.min=\u6700\u5C0F\u5145\u503C\u91D1\u984D\u70BA{0}
recharge.amout.max=\u6700\u5927\u5145\u503C\u91D1\u984D\u70BA{0}
currency.exchange_min = \u6700\u5C0F\u514C\u63DB\u91D1\u984D\u70BA{0}
currency.exchange_max = \u6700\u5927\u514C\u63DB\u91D1\u984D\u70BA{0}
exchange_error=\u514C\u63DB\u91D1\u984D\u5927\u65BC\u8CEC\u6236\u9918\u984D
exchange.record.exist.error=\u60A8\u6709\u4E00\u7B46\u514C\u63DB\u6B63\u5728\u9032\u884C\u4E2D\uFF0C\u7A0D\u5F8C\u518D\u8A66
exchange_symbol_error_exist =\u514C\u63DB\u5E63\u7A2E\u4E0D\u5B58\u5728
#\u79D2\u5408\u7EA6
order_amount_error=\u9322\u5305\u9918\u984D\u4E0D\u8DB3\uFF0C\u7121\u6CD5\u8CFC\u8CB7
order_10s_retry=\u4E0B\u55AE\u904E\u65BC\u983B\u7E41\uFF0C\u8ACB10s\u5F8C\u5617\u8A66\u4E0B\u55AE...
#\u7406\u8D22
user.push.message=\u7528\u6236\u64CD\u4F5C\u88AB\u7981\u6B62
mine.level.error=\u60A8\u7684VIP\u7B49\u7D1A\u4E0D\u5920\uFF0C\u7121\u6CD5\u8CFC\u8CB7\u6B21\u7406\u8CA1\u7522\u54C1
order.single.min=\u8CFC\u8CB7\u7684\u91D1\u984D\u5C11\u65BC\u6700\u4F4E\u9650\u984D\uFF0C\u7121\u6CD5\u8CFC\u8CB7
order.single.max=\u8CFC\u8CB7\u7684\u91D1\u984D\u5927\u65BC\u6700\u5927\u9650\u984D\uFF0C\u7121\u6CD5\u8CFC\u8CB7
order.buy.insufficient=\u60A8\u7576\u524D\u6700\u591A\u53EF\u8CFC\u8CB7\u91D1\u984D\u70BA{0}
product.removed=\u7522\u54C1\u5DF2\u4E0B\u67B6
financial.count.max=\u6B64\u7528\u6236\u5DF2\u5230\u9054\u9650\u5236\u6B21\u6578,\u4E0D\u5141\u8A31\u8CFC\u8CB7
days.not.null=\u8CFC\u8CB7\u9031\u671F\u70BA\u7A7A
days.not.error=\u8CFC\u8CB7\u9031\u671F\u932F\u8AA4

contract.accont.error=\u5408\u7D04\u8CEC\u6236\u9918\u984D\u4E0D\u8DB3
contract.min.share=\u6700\u4F4E\u8CFC\u8CB7\u6578\u70BA{0}
contract.max.share=\u6700\u9AD8\u8CFC\u8CB7\u6578\u70BA{0}
contract.asset.error=\u5408\u7D04\u8CEC\u6236\u9918\u984D\u4E0D\u8DB3
adjust.min.error=\u6E1B\u5C11\u7684\u4FDD\u8B49\u91D1\u4E0D\u80FD\u5C0F\u65BC\u521D\u59CB\u4FDD\u8B49\u91D1
order.status.error=\u975E\u6CD5\u63D0\u4EA4
contract.buy.earn.error=\u8CB7\u5165\u505A\u591A\uFF0C\u6B62\u76C8\u50F9\u4E0D\u80FD\u5C0F\u65BC\u958B\u5009\u5747\u50F9
contract.buy.loss.error=\u8CB7\u5165\u505A\u591A\uFF0C\u6B62\u640D\u50F9\u4E0D\u80FD\u5927\u65BC\u958B\u5009\u5747\u50F9
contract.sell.earn.error=\u8CE3\u51FA\u958B\u7A7A\uFF0C\u6B62\u76C8\u50F9\u4E0D\u80FD\u5927\u65BC\u958B\u5009\u5747\u50F9
contract.sell.loss.error=\u8CE3\u51FA\u958B\u7A7A,\u6B62\u640D\u50F9\u4E0D\u80FD\u5C0F\u65BC\u958B\u5009\u5747\u50F9
contract.num.limit=\u6578\u91CF\u4E0D\u80FD\u5927\u65BC\u6301\u5009\u91CF
#\u8D44\u91D1\u5212\u8F6C
asset_amount_error=\u9918\u984D\u4E0D\u8DB3

order.amount_error=\u9918\u984D\u4E0D\u8DB3

order.10s_retry=\u4E0B\u55AE\u904E\u65BC\u983B\u7E41\uFF0C\u8ACB10s\u5F8C\u5617\u8A66\u4E0B\u55AE...

back.code.exist.error = \u9280\u884C\u5361\u865F\u5DF2\u7D93\u5B58\u5728

contract.delivery.day=\u8DDD\u96E2\u4EA4\u5272\u6642\u9593\u9084\u6709\uFF1A{0}\u5929\u3002
contract.delivery.margan=\u8DDD\u96E2\u4EA4\u5272\u6642\u9593\u9084\u6709\uFF1A{0}\u5929\uFF1B\u5982\u60A8\u7684\u6301\u5009\u4FDD\u8B49\u91D1\u5927\u65BC\u7B49\u65BC{1} USDT \u6216\u6301\u5009\u4FDD\u8B49\u91D1\u5C0F\u65BC\u7B49\u65BC{2}USDT,\u53EF\u4EE5\u63D0\u524D\u5E73\u5009\u3002   
contract.delivery.earn=\u8DDD\u96E2\u4EA4\u5272\u6642\u9593\u9084\u6709\uFF1A{0}\u5929\uFF1B\u5982\u60A8\u7684\u6301\u5009\u4FDD\u8B49\u91D1\u5927\u65BC\u7B49\u65BC{1} USDT \u53EF\u4EE5\u63D0\u524D\u5E73\u5009\u3002
contract.delivery.loss=\u8DDD\u96E2\u4EA4\u5272\u6642\u9593\u9084\u6709\uFF1A{0}\u5929\uFF1B\u5982\u60A8\u7684\u6301\u5009\u4FDD\u8B49\u91D1\u7522\u5C0F\u65BC\u7B49\u65BC{1}USDT,\u53EF\u4EE5\u63D0\u524D\u5E73\u5009\u3002
order.audit.error=\u6B63\u5728\u5BA1\u6838\u4E2D
order.audit.pass=\u6B63\u5728\u5132\u503C\u4E2D\uFF0C\u9700\u8981\u5168\u74035\u500B\u7BC0\u9EDE\u78BA\u8A8D\uFF0C\u8ACB\u8010\u5FC3\u7B49\u5F85

#\u7533\u8D2D
own.coin.limit.num=\u53EF\u8CFC\u8CB7\u6578\u91CF{0}
own.coin.limit=\u53EF\u7528\u9918\u984D{0}
own.coin.success=\u7533\u8CFC\u6210\u529F
own.coin.error=\u7CFB\u7EDF\u9519\u8BEF\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458
own.coin.sub.play=\u60A8\u5DF2\u7533\u8CFC\uFF0C\u8ACB\u52FF\u91CD\u8907\u63D0\u4EA4\uFF01
own.coin.sub.error=\u672A\u8A02\u95B2\u6210\u529F\u4E0D\u53EF\u7533\u8CFC
own.coin.sub.success=\u60A8\u5DF2\u7372\u5F97 {0} \u7533\u8CFC\u8CC7\u683C
own.coin.sub.num.error=\u8ACB\u586B\u5BEB\u6B63\u78BA\u6578\u91CF
order.sell.min.error=\u6700\u4F4E\u8CE3\u51FA\u91CF\u70BA{0}
order.audit.reject=\u5E73\u5009\u5931\u6557
order_amount_limit=\u4E0D\u7B26\u5408\u91D1\u984D\u4E0A\u4E0B\u9650


#\u65B0\u53D1\u5E01\u8BA2\u9605
own.coin.subscribe.success=\u8A02\u95B2\u6210\u529F\uFF01
own.coin.subscribe.error=\u8A02\u95B2\u5931\u6557\uFF0C\u8ACB\u7A0D\u5F8C\u518D\u8A66
withdraw.kyc.error=\u8ACB\u5148\u5B8C\u6210\u5BE6\u540D\u8A8D\u8B49\uFF0C\u89E3\u9664\u9650\u984D
currency.coin.setting.nonexistent=\u7576\u524D{}\u914D\u7F6E\u4E0D\u5B58\u5728
currency.balance.deficiency=\u7576\u524D{}\u5E63\u7A2E\u9918\u984D\u4E0D\u8DB3
currency.deal.error=\u6B64\u5E63\u7A2E\u4E0D\u53EF\u4EA4\u6613

#\u5B9E\u540D\u8BA4\u8BC1
user.authentication.not.certified = \u8ACB\u5148\u9032\u884C\u521D\u7D1A\u8A8D\u8B49

#\u9ED1\u540D\u5355
user_is_black = \u60A8\u7684\u5E33\u865F\u5DF2\u5217\u5165\u9ED1\u540D\u55AE\uFF0C\u7121\u6CD5\u767B\u5165\u3002

#\u5E01\u5E01\u4EA4\u6613
currency.order.min.error= \u6700\u5C0F\u4E0B\u55AE\u91CF\u70BA {0}
currency.order.max.error= \u6700\u5927\u4E0B\u55AE\u91CF\u70BA {0}

