user.appname=\u8D8A\u5357\u8BED\u8BED\u8A00

not.null=* Y\u00EAu c\u1EA7u
user.jcaptcha.error=L\u1ED7i m\u00E3 x\u00E1c minh
user.jcaptcha.expire=M\u00E3 x\u00E1c minh \u0111\u00E3 h\u1EBFt h\u1EA1n
user.not.exists=Ng\u01B0\u1EDDi d\u00F9ng kh\u00F4ng t\u1ED3n t\u1EA1i/sai m\u1EADt kh\u1EA9u
user.password.not.match=Ng\u01B0\u1EDDi d\u00F9ng kh\u00F4ng t\u1ED3n t\u1EA1i/sai m\u1EADt kh\u1EA9u
user.password.retry.limit.count=M\u1EADt kh\u1EA9u \u0111\u00E3 nh\u1EADp sai {0} l\u1EA7n
user.password.retry.limit.exceed= N\u1EBFu nh\u1EADp sai m\u1EADt kh\u1EA9u {0} l\u1EA7n, t\u00E0i kho\u1EA3n s\u1EBD b\u1ECB kh\u00F3a trong {1} ph\u00FAt.
user.password.delete=Xin l\u1ED7i, t\u00E0i kho\u1EA3n c\u1EE7a b\u1EA1n \u0111\u00E3 b\u1ECB x\u00F3a
user.blocked=Ng\u01B0\u1EDDi d\u00F9ng \u0111\u00E3 b\u1ECB c\u1EA5m, vui l\u00F2ng li\u00EAn h\u1EC7 v\u1EDBi qu\u1EA3n tr\u1ECB vi\u00EAn
role.blocked=Vai tr\u00F2 \u0111\u00E3 b\u1ECB c\u1EA5m, vui l\u00F2ng li\u00EAn h\u1EC7 v\u1EDBi qu\u1EA3n tr\u1ECB vi\u00EAn
login.blocked=Th\u1EADt kh\u00F4ng may, IP truy c\u1EADp \u0111\u00E3 b\u1ECB \u0111\u01B0a v\u00E0o danh s\u00E1ch \u0111en c\u1EE7a h\u1EC7 th\u1ED1ng
user.logout.success=tho\u00E1t th\u00E0nh c\u00F4ng
user.ops.status=Ho\u1EA1t \u0111\u1ED9ng th\u00E0nh c\u00F4ng
length.not.valid=\u0110\u1ED9 d\u00E0i ph\u1EA3i n\u1EB1m trong kho\u1EA3ng t\u1EEB {min} \u0111\u1EBFn {max} k\u00FD t\u1EF1
user.username.not.valid=*  N\u00F3 bao g\u1ED3m t\u1EEB 2 \u0111\u1EBFn 20 k\u00FD t\u1EF1 ti\u1EBFng Trung, ch\u1EEF c\u00E1i, s\u1ED1 ho\u1EB7c d\u1EA5u g\u1EA1ch d\u01B0\u1EDBi v\u00E0 ph\u1EA3i b\u1EAFt \u0111\u1EA7u b\u1EB1ng m\u1ED9t s\u1ED1 kh\u00F4ng ph\u1EA3i s\u1ED1
user.password.not.valid=* 5-50 k\u00FD t\u1EF1
user.email.not.valid= L\u1ED7i \u0111\u1ECBnh d\u1EA1ng email
user.mobile.phone.number.not.valid=S\u1ED1 \u0111i\u1EC7n tho\u1EA1i kh\u00F4ng \u0111\u00FAng \u0111\u1ECBnh d\u1EA1ng
user.login.success=\u0111\u0103ng nh\u1EADp th\u00E0nh c\u00F4ng
user.register.success=\u0111\u0103ng k\u00FD th\u00E0nh c\u00F4ng
user.notfound=xin vui l\u00F2ng \u0111\u0103ng nh\u1EADp l\u1EA1i
user.forcelogout=Qu\u1EA3n tr\u1ECB vi\u00EAn bu\u1ED9c ph\u1EA3i \u0111\u0103ng xu\u1EA5t, vui l\u00F2ng \u0111\u0103ng nh\u1EADp l\u1EA1i
user.unknown.error=L\u1ED7i kh\u00F4ng x\u00E1c \u0111\u1ECBnh, vui l\u00F2ng \u0111\u0103ng nh\u1EADp l\u1EA1i
upload.exceed.maxSize=K\u00EDch th\u01B0\u1EDBc t\u1EC7p \u0111\u01B0\u1EE3c t\u1EA3i l\u00EAn v\u01B0\u1EE3t qu\u00E1 gi\u1EDBi h\u1EA1n k\u00EDch th\u01B0\u1EDBc t\u1EC7p! <br/>K\u00EDch th\u01B0\u1EDBc t\u1EC7p t\u1ED1i \u0111a \u0111\u01B0\u1EE3c ph\u00E9p l\u00E0: {0}MB!
upload.filename.exceed.length=T\u00EAn t\u1EC7p \u0111\u00E3 t\u1EA3i l\u00EAn ph\u1EA3i d\u00E0i t\u1ED1i \u0111a {0} k\u00FD t\u1EF1
no.permission=B\u1EA1n kh\u00F4ng c\u00F3 quy\u1EC1n truy c\u1EADp d\u1EEF li\u1EC7u, vui l\u00F2ng li\u00EAn h\u1EC7 v\u1EDBi qu\u1EA3n tr\u1ECB vi\u00EAn \u0111\u1EC3 th\u00EAm quy\u1EC1n [{0}]
no.create.permission=B\u1EA1n kh\u00F4ng c\u00F3 quy\u1EC1n t\u1EA1o d\u1EEF li\u1EC7u, vui l\u00F2ng li\u00EAn h\u1EC7 v\u1EDBi qu\u1EA3n tr\u1ECB vi\u00EAn \u0111\u1EC3 th\u00EAm quy\u1EC1n [{0}]
no.update.permission=B\u1EA1n kh\u00F4ng c\u00F3 quy\u1EC1n s\u1EEDa \u0111\u1ED5i d\u1EEF li\u1EC7u, vui l\u00F2ng li\u00EAn h\u1EC7 v\u1EDBi qu\u1EA3n tr\u1ECB vi\u00EAn \u0111\u1EC3 th\u00EAm quy\u1EC1n [{0}]
no.delete.permission=B\u1EA1n kh\u00F4ng c\u00F3 quy\u1EC1n x\u00F3a d\u1EEF li\u1EC7u, vui l\u00F2ng li\u00EAn h\u1EC7 v\u1EDBi qu\u1EA3n tr\u1ECB vi\u00EAn \u0111\u1EC3 th\u00EAm quy\u1EC1n [{0}]
no.export.permission=B\u1EA1n kh\u00F4ng c\u00F3 quy\u1EC1n xu\u1EA5t d\u1EEF li\u1EC7u, vui l\u00F2ng li\u00EAn h\u1EC7 v\u1EDBi qu\u1EA3n tr\u1ECB vi\u00EAn \u0111\u1EC3 th\u00EAm quy\u1EC1n [{0}]
no.view.permission=B\u1EA1n kh\u00F4ng c\u00F3 quy\u1EC1n xem d\u1EEF li\u1EC7u, vui l\u00F2ng li\u00EAn h\u1EC7 v\u1EDBi qu\u1EA3n tr\u1ECB vi\u00EAn \u0111\u1EC3 th\u00EAm quy\u1EC1n [{0}]
user.register.email.format=\u0110\u1ECBnh d\u1EA1ng email kh\u00F4ng \u0111\u00FAng
user.register.email.exisit=Email \u0111\u00E3 t\u1ED3n t\u1EA1i
user.register.phone.exisit=Email \u0111\u00E3 t\u1ED3n t\u1EA1i
user.user_name_exisit=t\u00EAn n\u00E0y \u0111\u00E3 c\u00F3 ng\u01B0\u1EDDi d\u00F9ng
login.user_error=sai t\u00EAn ng\u01B0\u1EDDi d\u00F9ng ho\u1EB7c m\u1EADt kh\u1EA9u
user.login.address.error=\u0110\u1ECBa ch\u1EC9 \u0111\u00E3 c\u00F3 ng\u01B0\u1EDDi s\u1EED d\u1EE5ng!
app.login.address.not.null=\u0110\u1ECBa ch\u1EC9 ng\u01B0\u1EDDi d\u00F9ng tr\u1ED1ng
login.email.not_register= Vui l\u00F2ng s\u1EED d\u1EE5ng \u0111\u1ECBa ch\u1EC9 email b\u1ECB r\u00E0ng bu\u1ED9c \u0111\u1EC3 ho\u1EA1t \u0111\u1ED9ng
login.phone.not_register=Vui l\u00F2ng s\u1EED d\u1EE5ng s\u1ED1 \u0111i\u1EC7n tho\u1EA1i di \u0111\u1ED9ng \u0111\u01B0\u1EE3c li\u00EAn k\u1EBFt \u0111\u1EC3 v\u1EADn h\u00E0nh
login.code_error=L\u1ED7i m\u00E3 x\u00E1c minh
user.login.code.error=X\u00E1c minh m\u00E3 x\u00E1c minh kh\u00F4ng th\u00E0nh c\u00F4ng!
user.login.password.null=Vui l\u00F2ng nh\u1EADp m\u1EADt kh\u1EA9u c\u1EE7a b\u1EA1n!
user.login.upd.success=\u0110\u00E3 s\u1EEDa \u0111\u1ED5i th\u00E0nh c\u00F4ng!
phone_code_empty=S\u1ED1 \u0111i\u1EC7n tho\u1EA1i di \u0111\u1ED9ng kh\u00F4ng \u0111\u01B0\u1EE3c \u0111\u1EC3 tr\u1ED1ng!
email.code_empty=E-mail kh\u00F4ng \u0111\u01B0\u1EE3c \u0111\u1EC3 tr\u1ED1ng!
user.code.send=M\u00E3 x\u00E1c minh \u0111\u00E3 \u0111\u01B0\u1EE3c g\u1EEDi th\u00E0nh c\u00F4ng
app.verification.email.code=M\u00E3 x\u00E1c minh \u0111\u00E3 \u0111\u01B0\u1EE3c g\u1EEDi t\u1EDBi email c\u1EE7a b\u1EA1n, vui l\u00F2ng ki\u1EC3m tra.
user.password_bind=M\u1EADt kh\u1EA9u \u0111\u00E3 \u0111\u01B0\u1EE3c \u0111\u1EB7t, vui l\u00F2ng kh\u00F4ng li\u00EAn k\u1EBFt l\u1EA1i.
user.tard.password_bind= M\u1EADt kh\u1EA9u b\u1EA3o m\u1EADt \u0111\u00E3 \u0111\u01B0\u1EE3c \u0111\u1EB7t, vui l\u00F2ng kh\u00F4ng li\u00EAn k\u1EBFt l\u1EA1i.
user.login.null=Ng\u01B0\u1EDDi d\u00F9ng kh\u00F4ng t\u1ED3n t\u1EA1i!
user.login.old.password=M\u1EADt kh\u1EA9u c\u0169 ch\u01B0a \u0111\u01B0\u1EE3c nh\u1EADp!
user.login.new.password=M\u1EADt kh\u1EA9u m\u1EDBi ch\u01B0a \u0111\u01B0\u1EE3c nh\u1EADp!
user.login.paw.upd=M\u1EADt kh\u1EA9u m\u1EDBi kh\u00F4ng \u0111\u01B0\u1EE3c gi\u1ED1ng m\u1EADt kh\u1EA9u c\u0169!
user.login.old.password.error=M\u1EADt kh\u1EA9u c\u0169 sai!
user.login.address.null=\u0110\u1ECBa ch\u1EC9 tr\u1ED1ng!
user.login.userid.null=ID ng\u01B0\u1EDDi d\u00F9ng tr\u1ED1ng!
user.password_notbind=H\u00E3y \u0111\u1EB7t m\u1EADt kh\u1EA9u an to\u00E0n
tard_password.error=Sai m\u1EADt kh\u1EA9u thanh to\u00E1n
#\u63D0\u73B0
withdraw.amount_number_exceed= R\u00FAt ti\u1EC1n v\u01B0\u1EE3t qu\u00E1 s\u1ED1 l\u1EA7n \u0111\u00E3 \u0111\u1EB7t trong ng\u00E0y
withdraw_error= S\u1ED1 d\u01B0 kh\u00F4ng \u0111\u1EE7, kh\u00F4ng th\u1EC3 r\u00FAt ti\u1EC1n m\u1EB7t
withdraw.amount_error=S\u1ED1 ti\u1EC1n r\u00FAt sai, vui l\u00F2ng s\u1EEDa l\u1EA1i.
withdraw.refresh=vui l\u00F2ng l\u00E0m m\u1EDBi
withdraw.address.isnull=Vui l\u00F2ng \u0111i\u1EC1n \u0111\u00FAng \u0111\u1ECBa ch\u1EC9 r\u00FAt ti\u1EC1n!
withdraw_error_coin=Lo\u1EA1i ti\u1EC1n t\u1EC7 n\u00E0y kh\u00F4ng t\u1ED3n t\u1EA1i
withdraw_error_rate=T\u1EF7 gi\u00E1 h\u1ED1i \u0111o\u00E1i n\u00E0y kh\u00F4ng t\u1ED3n t\u1EA1i
user.kyc.not_blank= T\u1EA5t c\u1EA3 b\u1ED1n m\u1EE5c th\u00F4ng tin x\u00E1c th\u1EF1c danh t\u00EDnh \u0111\u1EC1u \u0111\u01B0\u1EE3c y\u00EAu c\u1EA7u.
exchange.symbol.exist= Lo\u1EA1i ti\u1EC1n quy \u0111\u1ED5i kh\u00F4ng \u0111\u01B0\u1EE3c \u0111\u1EC3 tr\u1ED1ng v\u00E0 s\u1ED1 ti\u1EC1n quy \u0111\u1ED5i ph\u1EA3i l\u1EDBn h\u01A1n 0
recharge.amout.min=S\u1ED1 ti\u1EC1n g\u1EEDi t\u1ED1i thi\u1EC3u l\u00E0 {0}
recharge.amout.max=S\u1ED1 ti\u1EC1n n\u1EA1p t\u1ED1i \u0111a l\u00E0 {0}
currency.exchange_min=S\u1ED1 ti\u1EC1n trao \u0111\u1ED5i t\u1ED1i thi\u1EC3u l\u00E0 {0}
currency.exchange_max=S\u1ED1 ti\u1EC1n trao \u0111\u1ED5i t\u1ED1i \u0111a l\u00E0 {0}
exchange_error=S\u1ED1 ti\u1EC1n quy \u0111\u1ED5i l\u1EDBn h\u01A1n s\u1ED1 d\u01B0 t\u00E0i kho\u1EA3n
exchange.record.exist.error=B\u1EA1n \u0111ang ti\u1EBFn h\u00E0nh \u0111\u1ED5i qu\u00E0, vui l\u00F2ng th\u1EED l\u1EA1i sau
exchange_symbol_error_exist=\u0110\u1ED3ng ti\u1EC1n trao \u0111\u1ED5i kh\u00F4ng t\u1ED3n t\u1EA1i
order_amount_error= S\u1ED1 d\u01B0 trong v\u00ED kh\u00F4ng \u0111\u1EE7, kh\u00F4ng th\u1EC3 mua
order_10s_retry=\u0110\u01A1n h\u00E0ng \u0111\u01B0\u1EE3c \u0111\u1EB7t qu\u00E1 th\u01B0\u1EDDng xuy\u00EAn, vui l\u00F2ng th\u1EED \u0111\u1EB7t h\u00E0ng sau 10 gi\u00E2y...
user.push.message=Ho\u1EA1t \u0111\u1ED9ng c\u1EE7a ng\u01B0\u1EDDi d\u00F9ng b\u1ECB c\u1EA5m
mine.level.error=C\u1EA5p VIP c\u1EE7a b\u1EA1n kh\u00F4ng \u0111\u1EE7 \u0111\u1EC3 mua c\u00E1c s\u1EA3n ph\u1EA9m t\u00E0i ch\u00EDnh
order.single.min=S\u1ED1 ti\u1EC1n mua \u00EDt h\u01A1n gi\u1EDBi h\u1EA1n t\u1ED1i thi\u1EC3u v\u00E0 kh\u00F4ng th\u1EC3 mua \u0111\u01B0\u1EE3c
order.single.max=S\u1ED1 ti\u1EC1n mua l\u1EDBn h\u01A1n gi\u1EDBi h\u1EA1n t\u1ED1i \u0111a v\u00E0 kh\u00F4ng th\u1EC3 mua \u0111\u01B0\u1EE3c.
order.buy.insufficient=S\u1ED1 ti\u1EC1n t\u1ED1i \u0111a b\u1EA1n c\u00F3 th\u1EC3 mua hi\u1EC7n t\u1EA1i l\u00E0 {0}
product.removed=S\u1EA3n ph\u1EA9m \u0111\u00E3 \u0111\u01B0\u1EE3c g\u1EE1 b\u1ECF kh\u1ECFi k\u1EC7
financial.count.max=Ng\u01B0\u1EDDi d\u00F9ng n\u00E0y \u0111\u00E3 \u0111\u1EA1t \u0111\u1EBFn gi\u1EDBi h\u1EA1n v\u00E0 kh\u00F4ng \u0111\u01B0\u1EE3c ph\u00E9p mua h\u00E0ng.
days.not.null=Chu k\u1EF3 mua h\u00E0ng tr\u1ED1ng
days.not.error=L\u1ED7i chu k\u1EF3 mua h\u00E0ng
contract.accont.error=S\u1ED1 d\u01B0 trong t\u00E0i kho\u1EA3n h\u1EE3p \u0111\u1ED3ng kh\u00F4ng \u0111\u1EE7
contract.min.share=S\u1ED1 l\u01B0\u1EE3ng mua t\u1ED1i thi\u1EC3u l\u00E0 {0}
contract.max.share=S\u1ED1 l\u1EA7n mua t\u1ED1i \u0111a l\u00E0 {0}
contract.asset.error=S\u1ED1 d\u01B0 trong t\u00E0i kho\u1EA3n h\u1EE3p \u0111\u1ED3ng kh\u00F4ng \u0111\u1EE7
adjust.min.error=S\u1ED1 ti\u1EC1n k\u00FD qu\u1EF9 gi\u1EA3m kh\u00F4ng \u0111\u01B0\u1EE3c nh\u1ECF h\u01A1n s\u1ED1 ti\u1EC1n k\u00FD qu\u1EF9 ban \u0111\u1EA7u
order.status.error=N\u1ED9p b\u00E0i b\u1EA5t h\u1EE3p ph\u00E1p
contract.buy.earn.error=Mua l\u00E2u, gi\u00E1 ch\u1ED1t l\u1EDDi kh\u00F4ng th\u1EC3 th\u1EA5p h\u01A1n gi\u00E1 m\u1EDF c\u1EEDa trung b\u00ECnh
contract.buy.loss.error=Mua l\u00E2u, gi\u00E1 d\u1EEBng l\u1ED7 kh\u00F4ng \u0111\u01B0\u1EE3c l\u1EDBn h\u01A1n gi\u00E1 m\u1EDF c\u1EEDa trung b\u00ECnh
contract.sell.earn.error=B\u00E1n \u0111\u1EC3 m\u1EDF m\u1ED9t v\u1ECB th\u1EBF b\u00E1n, gi\u00E1 ch\u1ED1t l\u1EDDi kh\u00F4ng \u0111\u01B0\u1EE3c l\u1EDBn h\u01A1n gi\u00E1 m\u1EDF c\u1EEDa trung b\u00ECnh
contract.sell.loss.error=B\u00E1n v\u00E0 m\u1EDF l\u1EC7nh b\u00E1n, gi\u00E1 d\u1EEBng l\u1ED7 kh\u00F4ng th\u1EC3 th\u1EA5p h\u01A1n gi\u00E1 m\u1EDF c\u1EEDa trung b\u00ECnh
contract.num.limit=S\u1ED1 l\u01B0\u1EE3ng kh\u00F4ng \u0111\u01B0\u1EE3c l\u1EDBn h\u01A1n v\u1ECB tr\u00ED
asset_amount_error=Thi\u1EBFu c\u00E2n b\u1EB1ng
order.amount_error=S\u1ED1 d\u01B0 trong t\u00E0i kho\u1EA3n t\u00E0i ch\u00EDnh kh\u00F4ng \u0111\u1EE7
back.code.exist.error=S\u1ED1 th\u1EBB ng\u00E2n h\u00E0ng \u0111\u00E3 t\u1ED3n t\u1EA1i{}
contract.delivery.day= V\u1EABn c\u00F2n: {0} ng\u00E0y n\u1EEFa m\u1EDBi giao h\u00E0ng.
contract.delivery.margan=V\u1EABn c\u00F2n {0} ng\u00E0y tr\u01B0\u1EDBc khi giao h\u00E0ng; n\u1EBFu t\u00E0i s\u1EA3n th\u1EBF ch\u1EA5p v\u1ECB th\u1EBF c\u1EE7a b\u1EA1n l\u1EDBn h\u01A1n ho\u1EB7c b\u1EB1ng {1} USDT ho\u1EB7c t\u00E0i s\u1EA3n h\u1EE3p \u0111\u1ED3ng nh\u1ECF h\u01A1n ho\u1EB7c b\u1EB1ng {2} USDT, b\u1EA1n c\u00F3 th\u1EC3 \u0111\u00F3ng v\u1ECB th\u1EBF tr\u01B0\u1EDBc.
contract.delivery.earn=V\u1EABn c\u00F2n {0} ng\u00E0y tr\u01B0\u1EDBc th\u1EDDi \u0111i\u1EC3m giao h\u00E0ng; n\u1EBFu k\u00FD qu\u1EF9 v\u1ECB th\u1EBF c\u1EE7a b\u1EA1n l\u1EDBn h\u01A1n ho\u1EB7c b\u1EB1ng {1} USDT, b\u1EA1n c\u00F3 th\u1EC3 \u0111\u00F3ng v\u1ECB th\u1EBF tr\u01B0\u1EDBc.
contract.delivery.loss=V\u1EABn c\u00F2n: {0} ng\u00E0y tr\u01B0\u1EDBc th\u1EDDi gian giao h\u00E0ng; n\u1EBFu t\u00E0i s\u1EA3n th\u1EBF ch\u1EA5p v\u1ECB th\u1EBF c\u1EE7a b\u1EA1n nh\u1ECF h\u01A1n ho\u1EB7c b\u1EB1ng {1}USDT, b\u1EA1n c\u00F3 th\u1EC3 \u0111\u00F3ng v\u1ECB th\u1EBF tr\u01B0\u1EDBc.
order.audit.error=\u0110ang xem x\u00E9t
order.audit.pass= \u0111ang \u0111\u01B0\u1EE3c s\u1EA1c l\u1EA1i v\u00E0 c\u1EA7n \u0111\u01B0\u1EE3c 5 n\u00FAt tr\u00EAn to\u00E0n th\u1EBF gi\u1EDBi x\u00E1c nh\u1EADn. Vui l\u00F2ng ki\u00EAn nh\u1EABn ch\u1EDD \u0111\u1EE3i.
withdraw.kyc.error=Vui l\u00F2ng ho\u00E0n t\u1EA5t x\u00E1c th\u1EF1c t\u00EAn th\u1EADt tr\u01B0\u1EDBc \u0111\u1EC3 n\u00E2ng gi\u1EDBi h\u1EA1n
currency.coin.setting.nonexistent=C\u1EA5u h\u00ECnh {} hi\u1EC7n t\u1EA1i kh\u00F4ng t\u1ED3n t\u1EA1i
currency.balance.deficiency=S\u1ED1 d\u01B0 ti\u1EC1n t\u1EC7 {} hi\u1EC7n t\u1EA1i kh\u00F4ng \u0111\u1EE7
currency.deal.error=Lo\u1EA1i ti\u1EC1n n\u00E0y kh\u00F4ng th\u1EC3 giao d\u1ECBch \u0111\u01B0\u1EE3c
order.10s_retry=\u0110\u01A1n h\u00E0ng \u0111\u01B0\u1EE3c \u0111\u1EB7t qu\u00E1 th\u01B0\u1EDDng xuy\u00EAn, vui l\u00F2ng th\u1EED \u0111\u1EB7t h\u00E0ng sau 10 gi\u00E2y...
own.coin.limit.num=S\u1ED1 l\u01B0\u1EE3ng c\u00F3 s\u1EB5n {0}
own.coin.limit=S\u1ED1 d\u01B0 kh\u1EA3 d\u1EE5ng {0}
own.coin.success=\u0110\u0103ng k\u00FD theo d\u00F5i th\u00E0nh c\u00F4ng
own.coin.error=L\u1ED7i h\u1EC7 th\u1ED1ng, vui l\u00F2ng li\u00EAn h\u1EC7 qu\u1EA3n tr\u1ECB vi\u00EAn
own.coin.sub.play=B\u1EA1n \u0111\u00E3 \u0111\u0103ng k\u00FD r\u1ED3i, vui l\u00F2ng kh\u00F4ng g\u1EEDi l\u1EA1i!
own.coin.sub.error=\u0110\u0103ng k\u00FD kh\u00F4ng c\u00F3 s\u1EB5n n\u1EBFu b\u1EA1n ch\u01B0a \u0111\u0103ng k\u00FD th\u00E0nh c\u00F4ng
own.coin.sub.success=B\u1EA1n \u0111\u00E3 \u0111\u1EA1t \u0111\u01B0\u1EE3c {0} b\u1EB1ng c\u1EA5p \u0111\u0103ng k\u00FD
own.coin.sub.num.error=Vui l\u00F2ng \u0111i\u1EC1n \u0111\u00FAng s\u1ED1 l\u01B0\u1EE3ng
order.sell.min.error=S\u1ED1 ti\u1EC1n b\u00E1n t\u1ED1i thi\u1EC3u l\u00E0 {0}
order.audit.reject=Kh\u00F4ng th\u1EC3 \u0111\u00F3ng v\u1ECB th\u1EBF
own.coin.subscribe.success=\u0110\u0103ng k\u00FD theo d\u00F5i th\u00E0nh c\u00F4ng
own.coin.subscribe.error=\u0110\u0103ng k\u00FD kh\u00F4ng th\u00E0nh c\u00F4ng
user.authentication.not.certified = Vui l\u00F2ng th\u1EF1c hi\u1EC7n ch\u1EE9ng nh\u1EADn ch\u00EDnh tr\u01B0\u1EDBc

#\u9ED1\u540D\u5355
user_is_black = T\u00E0i kho\u1EA3n c\u1EE7a b\u1EA1n \u0111\u00E3 b\u1ECB li\u1EC7t v\u00E0o danh s\u00E1ch \u0111en v\u00E0 kh\u00F4ng th\u1EC3 \u0111\u0103ng nh\u1EADp \u0111\u01B0\u1EE3c.

withdraw_require_error = Hi\u1EC7n t\u1EA1i t\u00F4i v\u1EABn c\u1EA7n ch\u01A1i v\u1EDBi s\u1ED1 doanh thu c\u00F2n l\u1EA1i.

#\u5E01\u5E01\u4EA4\u6613
currency.order.min.error= S\u1ED1 l\u01B0\u1EE3ng \u0111\u1EB7t h\u00E0ng t\u1ED1i thi\u1EC3u l\u00E0 {0}
currency.order.max.error= S\u1ED1 l\u01B0\u1EE3ng \u0111\u1EB7t h\u00E0ng t\u1ED1i \u0111a l\u00E0 {0}

