user.appname=\u97E9\u8BED\u8BED\u8A00
#\u9519\u8BEF\u6D88\u606F
not.null=* \uD544\uC218\uC758
user.jcaptcha.error=\uC778\uC99D \uCF54\uB4DC \uC624\uB958
user.jcaptcha.expire=\uC778\uC99D \uCF54\uB4DC\uAC00 \uB9CC\uB8CC\uB418\uC5C8\uC2B5\uB2C8\uB2E4.
user.not.exists=\uC0AC\uC6A9\uC790\uAC00 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4/\uC798\uBABB\uB41C \uBE44\uBC00\uBC88\uD638
user.password.not.match=\uC0AC\uC6A9\uC790\uAC00 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4/\uC798\uBABB\uB41C \uBE44\uBC00\uBC88\uD638
user.password.retry.limit.count=\uC798\uBABB\uB41C \uBE44\uBC00\uBC88\uD638\uB97C {0}\uBC88 \uC785\uB825\uD588\uC2B5\uB2C8\uB2E4.
user.password.retry.limit.exceed=\uBE44\uBC00\uBC88\uD638\uAC00 {0}\uBC88 \uC798\uBABB \uC785\uB825\uB418\uC5B4 \uACC4\uC815\uC774 {1}\uBD84 \uB3D9\uC548 \uC7A0\uACBC\uC2B5\uB2C8\uB2E4.
user.password.delete=\uC8C4\uC1A1\uD569\uB2C8\uB2E4. \uACC4\uC815\uC774 \uC0AD\uC81C\uB418\uC5C8\uC2B5\uB2C8\uB2E4.
user.blocked=\uC0AC\uC6A9\uC790\uAC00 \uCC28\uB2E8\uB418\uC5C8\uC2B5\uB2C8\uB2E4. \uAD00\uB9AC\uC790\uC5D0\uAC8C \uBB38\uC758\uD558\uC2ED\uC2DC\uC624.
role.blocked=\uC5ED\uD560\uC774 \uAE08\uC9C0\uB418\uC5C8\uC2B5\uB2C8\uB2E4. \uAD00\uB9AC\uC790\uC5D0\uAC8C \uBB38\uC758\uD558\uC2ED\uC2DC\uC624.
login.blocked=\uC548\uD0C0\uAE5D\uAC8C\uB3C4 \uC561\uC138\uC2A4 IP\uAC00 \uC2DC\uC2A4\uD15C\uC5D0 \uC758\uD574 \uBE14\uB799\uB9AC\uC2A4\uD2B8\uC5D0 \uC62C\uB790\uC2B5\uB2C8\uB2E4.
user.logout.success=\uC131\uACF5\uC801\uC73C\uB85C \uC885\uB8CC
user.ops.status=\uC131\uACF5\uC801\uC778 \uC6B4\uC601

length.not.valid=\uAE38\uC774\uB294 {min}\uC5D0\uC11C {max}\uC790 \uC0AC\uC774\uC5EC\uC57C \uD569\uB2C8\uB2E4.

user.username.not.valid=* 2~20\uC790\uC758 \uD55C\uC790, \uBB38\uC790, \uC22B\uC790 \uB610\uB294 \uBC11\uC904\uC774\uBA70 \uC22B\uC790\uAC00 \uC544\uB2CC \uBB38\uC790\uB85C \uC2DC\uC791\uD574\uC57C \uD569\uB2C8\uB2E4.
user.password.not.valid=* 5-50\uC790

user.email.not.valid=\u90AE\u7BB1\u683C\u5F0F\u9519\u8BEF
user.mobile.phone.number.not.valid=\u624B\u673A\u53F7\u683C\u5F0F\u9519\u8BEF
user.login.success=\u767B\u5F55\u6210\u529F
user.register.success=\u6CE8\u518C\u6210\u529F
user.notfound=\u8BF7\u91CD\u65B0\u767B\u5F55
user.forcelogout=\u7BA1\u7406\u5458\u5F3A\u5236\u9000\u51FA\uFF0C\u8BF7\u91CD\u65B0\u767B\u5F55
user.unknown.error=\u672A\u77E5\u9519\u8BEF\uFF0C\u8BF7\u91CD\u65B0\u767B\u5F55
user.register.phone.exist=\uD734\uB300\uD3F0 \uBC88\uD638\uAC00 \uC774\uBBF8 \uC874\uC7AC\uD569\uB2C8\uB2E4
user.register.phone.bind=\uD734\uB300\uD3F0 \uBC88\uD638\uAC00 \uBC14\uC778\uB529\uB418\uC5C8\uC2B5\uB2C8\uB2E4
##\u6587\u4EF6\u4E0A\u4F20\u6D88\u606F
upload.exceed.maxSize=\u4E0A\u4F20\u7684\u6587\u4EF6\u5927\u5C0F\u8D85\u51FA\u9650\u5236\u7684\u6587\u4EF6\u5927\u5C0F\uFF01<br/>\u5141\u8BB8\u7684\u6587\u4EF6\u6700\u5927\u5927\u5C0F\u662F\uFF1A{0}MB\uFF01
upload.filename.exceed.length=\u4E0A\u4F20\u7684\u6587\u4EF6\u540D\u6700\u957F{0}\u4E2A\u5B57\u7B26

##\u6743\u9650
no.permission=\u60A8\u6CA1\u6709\u6570\u636E\u7684\u6743\u9650\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\u6DFB\u52A0\u6743\u9650 [{0}]
no.create.permission=\u60A8\u6CA1\u6709\u521B\u5EFA\u6570\u636E\u7684\u6743\u9650\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\u6DFB\u52A0\u6743\u9650 [{0}]
no.update.permission=\u60A8\u6CA1\u6709\u4FEE\u6539\u6570\u636E\u7684\u6743\u9650\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\u6DFB\u52A0\u6743\u9650 [{0}]
no.delete.permission=\u60A8\u6CA1\u6709\u5220\u9664\u6570\u636E\u7684\u6743\u9650\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\u6DFB\u52A0\u6743\u9650 [{0}]
no.export.permission=\u60A8\u6CA1\u6709\u5BFC\u51FA\u6570\u636E\u7684\u6743\u9650\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\u6DFB\u52A0\u6743\u9650 [{0}]
no.view.permission=\u60A8\u6CA1\u6709\u67E5\u770B\u6570\u636E\u7684\u6743\u9650\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\u6DFB\u52A0\u6743\u9650 [{0}]

##\u6CE8\u518C\u7528\u6237\u63D0\u793A
user.register.email.format=\uC774\uBA54\uC77C \uD615\uC2DD\uC774 \uC798\uBABB\uB418\uC5C8\uC2B5\uB2C8\uB2E4.
user.register.email.exisit=\uC0AC\uC11C\uD568\uC774 \uC774\uBBF8 \uC874\uC7AC\uD569\uB2C8\uB2E4.
user.register.phone.exisit=\uD734\uB300\uC804\uD654 \uBC88\uD638\uAC00 \uC774\uBBF8 \uC874\uC7AC\uD569\uB2C8\uB2E4.
user.user_name_exisit=\uC0AC\uC6A9\uC790 \uC774\uB984\uC774 \uC774\uBBF8 \uC874\uC7AC\uD569\uB2C8\uB2E4
login.user_error=\uC798\uBABB\uB41C \uC0AC\uC6A9\uC790 \uC774\uB984 \uB610\uB294 \uC554\uD638
user.login.address.error=\uC8FC\uC18C\uAC00 \uCC0D\uD614\uC2B5\uB2C8\uB2E4!
#app
app.login.address.not.null=\uC0AC\uC6A9\uC790 \uC8FC\uC18C\uAC00 \uBE44\uC5B4 \uC788\uC2B5\uB2C8\uB2E4
login.email.not_register=\uBC14\uC778\uB529\uB41C \uC774\uBA54\uC77C \uC8FC\uC18C\uB97C \uC0AC\uC6A9\uD558\uC5EC \uC791\uB3D9\uD558\uC2ED\uC2DC\uC624.
login.phone.not_register=\uC5F0\uB3D9\uB41C \uD734\uB300\uD3F0 \uBC88\uD638\uB97C \uC774\uC6A9\uD558\uC5EC \uC6B4\uC601\uD574\uC8FC\uC138\uC694
login.code_error=\uC778\uC99D \uCF54\uB4DC \uC624\uB958
user.login.code.error=\uC778\uC99D \uCF54\uB4DC \uD655\uC778\uC5D0 \uC2E4\uD328\uD588\uC2B5\uB2C8\uB2E4!
user.login.password.null=\uBE44\uBC00\uBC88\uD638\uB97C \uC785\uB825\uD574\uC8FC\uC138\uC694!
user.login.upd.success=\uC131\uACF5\uC801\uC73C\uB85C \uC218\uC815\uB418\uC5C8\uC2B5\uB2C8\uB2E4!
phone_code_empty=\uD734\uB300\uD3F0 \uBC88\uD638\uB294 \uBE44\uC6CC\uB458 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4!
email.code_empty=\uC774\uBA54\uC77C\uC740 \uBE44\uC6CC\uB458 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4!
user.code.send=\uC778\uC99D \uCF54\uB4DC\uAC00 \uC131\uACF5\uC801\uC73C\uB85C \uC804\uC1A1\uB418\uC5C8\uC2B5\uB2C8\uB2E4.
app.verification.email.code=\uC778\uC99D\uBC88\uD638\uAC00 \uC774\uBA54\uC77C\uB85C \uBC1C\uC1A1\uB418\uC5C8\uC73C\uB2C8 \uD655\uC778 \uBD80\uD0C1\uB4DC\uB9BD\uB2C8\uB2E4.
user.password_bind=\uBE44\uBC00\uBC88\uD638\uAC00 \uC774\uBBF8 \uC124\uC815\uB418\uC5B4 \uC788\uC2B5\uB2C8\uB2E4. \uB2E4\uC2DC \uBB36\uC9C0 \uB9C8\uC2ED\uC2DC\uC624.
user.tard.password_bind=\uBCF4\uC548 \uC554\uD638\uAC00 \uC124\uC815\uB418\uC5C8\uC2B5\uB2C8\uB2E4. \uB2E4\uC2DC \uBC14\uC778\uB529\uD558\uC9C0 \uB9C8\uC2ED\uC2DC\uC624.
user.login.null=\uC0AC\uC6A9\uC790\uAC00 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4!
user.login.old.password=\uC774\uC804 \uC554\uD638\uAC00 \uC785\uB825\uB418\uC9C0 \uC54A\uC558\uC2B5\uB2C8\uB2E4!
user.login.new.password=\uC0C8 \uBE44\uBC00\uBC88\uD638\uAC00 \uC785\uB825\uB418\uC9C0 \uC54A\uC558\uC2B5\uB2C8\uB2E4!
user.login.paw.upd=\uC0C8 \uBE44\uBC00\uBC88\uD638\uB294 \uC774\uC804 \uBE44\uBC00\uBC88\uD638\uC640 \uAC19\uC744 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4!
user.login.old.password.error=\uC774\uC804 \uC554\uD638\uAC00 \uC798\uBABB\uB418\uC5C8\uC2B5\uB2C8\uB2E4!
user.login.address.null=\uC8FC\uC18C\uAC00 \uBE44\uC5B4 \uC788\uC2B5\uB2C8\uB2E4!
user.login.userid.null=\uC0AC\uC6A9\uC790 ID\uAC00 \uBE44\uC5B4 \uC788\uC2B5\uB2C8\uB2E4!
#\u63D0\u73B0
user.password_notbind=\uC548\uC804\uD55C \uBE44\uBC00\uBC88\uD638\uB97C \uC124\uC815\uD574\uC8FC\uC138\uC694
tard_password.error=\uC798\uBABB\uB41C \uACB0\uC81C \uBE44\uBC00\uBC88\uD638
withdraw.amount_number_exceed=\uB2F9\uC77C \uCD9C\uAE08\uD69F\uC218\uAC00 \uC124\uC815\uD55C \uD69F\uC218\uB97C \uCD08\uACFC\uD55C \uACBD\uC6B0
withdraw_error=\uC794\uC561 \uBD80\uC871, \uD604\uAE08 \uC778\uCD9C \uBD88\uAC00
withdraw.amount_error=\uCD9C\uAE08\uAE08\uC561\uC774 \uD2C0\uB838\uC5B4\uC694 \uC218\uC815\uD574\uC8FC\uC138\uC694
withdraw.refresh=\uC0C8\uB85C \uACE0\uCE68\uD558\uC2ED\uC2DC\uC624
withdraw.address.isnull=\uC815\uD655\uD55C \uCD9C\uAE08\uC8FC\uC18C\uB97C \uC785\uB825\uD574\uC8FC\uC138\uC694!
withdraw_require_error = \uD604\uC7AC\uB294 \uB0A8\uC740 \uB9E4\uCD9C\uC561\uC744 \uAC00\uC9C0\uACE0 \uB180\uC544\uC57C\uD569\uB2C8\uB2E4.
withdraw_error_coin=\uC774 \uD1B5\uD654\uB294 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
withdraw_error_rate=\uC774 \uD658\uC728\uC740 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
#\u5B9E\u540D\u8BA4\u8BC1
user.kyc.not_blank=\uBCF8\uC778\uC778\uC99D \uC815\uBCF4 4\uAC1C \uD56D\uBAA9 \uBAA8\uB450 \uD544\uC218

#\u5151\u6362
exchange.symbol.exist = \uAD50\uD658 \uD1B5\uD654\uB294 \uBE44\uC6CC\uB458 \uC218 \uC5C6\uC73C\uBA70 \uAD50\uD658 \uAE08\uC561\uC740 0\uBCF4\uB2E4 \uCEE4\uC57C \uD569\uB2C8\uB2E4.
recharge.amout.min=\uCD5C\uC18C \uCDA9\uC804 \uAE08\uC561\uC740 {0}\uC785\uB2C8\uB2E4.
recharge.amout.max=\uCD5C\uB300 \uCDA9\uC804 \uAE08\uC561\uC740 {0}\uC785\uB2C8\uB2E4.
currency.exchange_min = \uCD5C\uC18C \uAD50\uD658 \uAE08\uC561\uC740 {0}\uC785\uB2C8\uB2E4.
currency.exchange_max =\uCD5C\uB300 \uAD50\uD658 \uAE08\uC561\uC740 {0}\uC785\uB2C8\uB2E4.
exchange_error=\uAD50\uD658 \uAE08\uC561\uC774 \uACC4\uC815 \uC794\uC561\uBCF4\uB2E4 \uD07D\uB2C8\uB2E4.
exchange.record.exist.error=\uC0AC\uC6A9\uC774 \uC9C4\uD589 \uC911\uC785\uB2C8\uB2E4. \uB098\uC911\uC5D0 \uB2E4\uC2DC \uC2DC\uB3C4\uD558\uC138\uC694.
#\u79D2\u5408\u7EA6
order_amount_error=\uC9C0\uAC11 \uC794\uC561 \uBD80\uC871, \uAD6C\uB9E4\uD560 \uC218 \uC5C6\uC74C
order_10s_retry=\uC8FC\uBB38\uC774 \uB108\uBB34 \uC7A6\uC2B5\uB2C8\uB2E4. 10\uCD08 \uC774\uD6C4\uC5D0 \uC8FC\uBB38\uD574 \uC8FC\uC138\uC694...
#\u7406\u8D22
user.push.message=\uC0AC\uC6A9\uC790 \uC791\uC5C5 \uAE08\uC9C0
mine.level.error=\uADC0\uD558\uC758 VIP \uB4F1\uAE09\uC740 2\uCC28 \uC790\uC0B0 \uAD00\uB9AC \uC0C1\uD488\uC744 \uAD6C\uB9E4\uD558\uAE30\uC5D0 \uCDA9\uBD84\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
order.single.min=\uAD6C\uB9E4 \uAE08\uC561\uC774 \uCD5C\uC18C \uAE08\uC561 \uBBF8\uB9CC\uC73C\uB85C \uAD6C\uB9E4\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
order.single.max=\uAD6C\uB9E4\uD55C \uAE08\uC561\uC774 \uCD5C\uB300 \uD55C\uB3C4\uB97C \uCD08\uACFC\uD558\uC5EC \uAD6C\uB9E4\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
order.buy.insufficient=\uD604\uC7AC \uCD5C\uB300 {0}\uAE4C\uC9C0 \uAD6C\uB9E4\uD560 \uC218 \uC788\uC2B5\uB2C8\uB2E4.
product.removed=\uC81C\uD488\uC774 \uB2E8\uC885\uB418\uC5C8\uC2B5\uB2C8\uB2E4
financial.count.max=\uC774 \uC0AC\uC6A9\uC790\uB294 \uD55C\uB3C4\uC5D0 \uB3C4\uB2EC\uD558\uC5EC \uAD6C\uB9E4\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
days.not.null=\uAD6C\uB9E4 \uC8FC\uAE30\uAC00 \uBE44\uC5B4 \uC788\uC2B5\uB2C8\uB2E4.
days.not.error=\uAD6C\uB9E4\uC8FC\uAE30 \uC624\uB958

contract.accont.error=\uBD88\uCDA9\uBD84\uD55C \uACC4\uC57D \uACC4\uC815 \uC794\uC561
contract.min.share=\uCD5C\uC18C \uAD6C\uB9E4 \uC218\uB7C9\uC740 {0}\uC785\uB2C8\uB2E4.
contract.max.share=\uCD5C\uB300 \uAD6C\uB9E4 \uC218\uB294 {0}\uC785\uB2C8\uB2E4.
contract.asset.error=\uBD88\uCDA9\uBD84\uD55C \uACC4\uC57D \uACC4\uC815 \uC794\uC561
adjust.min.error=\uAC10\uC18C \uC99D\uAC70\uAE08\uC740 \uCD08\uAE30 \uC99D\uAC70\uAE08\uBCF4\uB2E4 \uC791\uC744 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
order.status.error=\uBD88\uBC95 \uC81C\uCD9C
contract.buy.earn.error=\uB9E4\uC218, \uC774\uC775 \uC2E4\uD604 \uAC00\uACA9\uC740 \uD3C9\uADE0 \uC2DC\uAC00\uBCF4\uB2E4 \uB0AE\uC744 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
contract.buy.loss.error=\uB9E4\uC218 \uB9E4\uC218, \uC190\uC808\uB9E4 \uAC00\uACA9\uC740 \uD3C9\uADE0 \uC2DC\uAC00\uBCF4\uB2E4 \uB192\uC744 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
contract.sell.earn.error=\uC20F \uD3EC\uC9C0\uC158\uC744 \uC5F4\uAE30 \uC704\uD574 \uB9E4\uB3C4, \uC774\uC775 \uC2E4\uD604 \uAC00\uACA9\uC740 \uD3C9\uADE0 \uAC1C\uC2DC \uAC00\uACA9\uBCF4\uB2E4 \uB192\uC744 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
contract.sell.loss.error=\uC20F \uD3EC\uC9C0\uC158\uC744 \uC5F4\uAE30 \uC704\uD574 \uB9E4\uB3C4, \uC190\uC808\uB9E4 \uAC00\uACA9\uC740 \uD3C9\uADE0 \uAC1C\uC2DC \uAC00\uACA9\uBCF4\uB2E4 \uB0AE\uC744 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
contract.num.limit=\uC218\uB7C9\uC740 \uBBF8\uACB0\uC81C\uC57D\uC815\uBCF4\uB2E4 \uD074 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
#\u8D44\u91D1\u5212\u8F6C
asset_amount_error=\uC794\uC561 \uBD88\uCDA9\uBD84
order.amount_error=\uC790\uC0B0\uAD00\uB9AC \uACC4\uC815 \uC794\uC561 \uBD80\uC871
order.10s_retry=\uC8FC\uBB38\uC774 \uB108\uBB34 \uC790\uC8FC \uB4E4\uC5B4\uC624\uB124\uC694. 10\uCD08 \uD6C4\uC5D0 \uC8FC\uBB38\uD574 \uBCF4\uC138\uC694...
order_amount_limit=\uBD88\uBC95\uAE08\uC561
contract.delivery.day=\uC544\uC9C1 \uBC30\uC1A1\uAE4C\uC9C0 {0}\uC77C \uB0A8\uC558\uC2B5\uB2C8\uB2E4.
contract.delivery.margan=\uC544\uC9C1 \uBC30\uC1A1\uC774 {0}\uC77C \uB0A8\uC558\uC2B5\uB2C8\uB2E4. \uD3EC\uC9C0\uC158 \uC99D\uAC70\uAE08\uC774 {1} USDT \uC774\uC0C1\uC774\uAC70\uB098 \uD3EC\uC9C0\uC158 \uC99D\uAC70\uAE08\uC774 {2} USDT \uC774\uD558\uC778 \uACBD\uC6B0 \uC0AC\uC804\uC5D0 \uD3EC\uC9C0\uC158\uC744 \uCCAD\uC0B0\uD560 \uC218 \uC788\uC2B5\uB2C8\uB2E4.
contract.delivery.earn=\uBC30\uC1A1\uC77C\uAE4C\uC9C0\uB294 \uC544\uC9C1 {0}\uC77C \uB0A8\uC558\uC2B5\uB2C8\uB2E4. \uD3EC\uC9C0\uC158 \uC99D\uAC70\uAE08\uC774 {1} USDT \uC774\uC0C1\uC778 \uACBD\uC6B0 \uBBF8\uB9AC \uD3EC\uC9C0\uC158\uC744 \uCCAD\uC0B0\uD560 \uC218 \uC788\uC2B5\uB2C8\uB2E4.
contract.delivery.loss=\uC544\uC9C1 \uBC30\uC1A1 \uC2DC\uAC04\uC774 {0}\uC77C \uB0A8\uC558\uC2B5\uB2C8\uB2E4. \uD3EC\uC9C0\uC158 \uC99D\uAC70\uAE08\uC774 {1}USDT \uC774\uD558\uC778 \uACBD\uC6B0 \uBBF8\uB9AC \uD3EC\uC9C0\uC158\uC744 \uCCAD\uC0B0\uD560 \uC218 \uC788\uC2B5\uB2C8\uB2E4.
order.audit.error=\uAC80\uD1A0\uC911
order.audit.pass=\uCDA9\uC804\uC774 \uC9C4\uD589 \uC911\uC774\uBA70 \uC804 \uC138\uACC4 5\uAC1C \uB178\uB4DC\uC758 \uD655\uC778\uC774 \uD544\uC694\uD569\uB2C8\uB2E4. \uC7A0\uC2DC \uAE30\uB2E4\uB824 \uC8FC\uC2ED\uC2DC\uC624.

#\u7533\u8D2D
own.coin.limit.num= \uC0AC\uC6A9 \uAC00\uB2A5 \uC218\uB7C9 {0}
own.coin.limit=\uC0AC\uC6A9 \uAC00\uB2A5\uD55C \uC794\uC561 {0}
own.coin.success=\uAD6C\uB3C5 \uC131\uACF5
own.coin.error=\uC2DC\uC2A4\uD15C \uC624\uB958\uC785\uB2C8\uB2E4. \uAD00\uB9AC\uC790\uC5D0\uAC8C \uBB38\uC758\uD558\uC138\uC694
own.coin.sub.play=\uC774\uBBF8 \uAD6C\uB3C5\uD558\uC168\uC2B5\uB2C8\uB2E4. \uB2E4\uC2DC \uC81C\uCD9C\uD558\uC9C0 \uB9C8\uC138\uC694!
own.coin.sub.error=\uC131\uACF5\uC801\uC73C\uB85C \uAD6C\uB3C5\uD558\uC9C0 \uC54A\uC740 \uACBD\uC6B0 \uAD6C\uB3C5\uC744 \uC0AC\uC6A9\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
own.coin.sub.success=\uB2F9\uC2E0\uC740 \uC5BB\uC5C8\uC2B5\uB2C8\uB2E4 {0} \uAD6C\uB3C5 \uC790\uACA9
own.coin.sub.num.error=\uC815\uD655\uD55C \uC218\uB7C9\uC744 \uC785\uB825\uD574\uC8FC\uC138\uC694.
order.sell.min.error=\uCD5C\uC18C \uD310\uB9E4 \uAE08\uC561\uC740 {0}\uC785\uB2C8\uB2E4.
order.audit.reject=\uD3EC\uC9C0\uC158\uC744 \uCCAD\uC0B0\uD558\uC9C0 \uBABB\uD588\uC2B5\uB2C8\uB2E4.

#\u65B0\u53D1\u5E01\u8BA2\u9605
own.coin.subscribe.success=\uAD6C\uB3C5 \uC131\uACF5
own.coin.subscribe.error=\uAD6C\uB3C5 \uC2E4\uD328
withdraw.kyc.error=\uD55C\uB3C4\uD574\uC81C\uB97C \uC704\uD574 \uBA3C\uC800 \uC2E4\uBA85\uC778\uC99D\uC744 \uC644\uB8CC\uD574\uC8FC\uC138\uC694.
exchange_symbol_error_exist=\uAD50\uD658 \uD1B5\uD654\uAC00 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4
currency.coin.setting.nonexistent=\uD604\uC7AC {} \uAD6C\uC131\uC774 \uC874\uC7AC\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
currency.balance.deficiency=\uD604\uC7AC {} \uD1B5\uD654 \uC794\uC561\uC774 \uBD80\uC871\uD569\uB2C8\uB2E4.
currency.deal.error=\uC774 \uD1B5\uD654\uB294 \uAC70\uB798\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
back.code.exist.error=\uC740\uD589 \uCE74\uB4DC \uBC88\uD638\uAC00 \uC774\uBBF8 \uC874\uC7AC\uD569\uB2C8\uB2E4.{}

#\u5B9E\u540D\u8BA4\u8BC1
user.authentication.not.certified = \uAE30\uBCF8 \uC778\uC99D\uC744 \uBA3C\uC800 \uC218\uD589\uD574 \uC8FC\uC138\uC694.

#\u9ED1\u540D\u5355
user_is_black = \uADC0\uD558\uC758 \uACC4\uC815\uC740 \uBE14\uB799\uB9AC\uC2A4\uD2B8\uC5D0 \uB4F1\uB85D\uB418\uC5B4 \uB85C\uADF8\uC778\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.

#\u5E01\u5E01\u4EA4\u6613
currency.order.min.error= \uCD5C\uC18C \uC8FC\uBB38 \uC218\uB7C9\uC740 {0}
currency.order.max.error= \uCD5C\uB300 \uC8FC\uBB38 \uC218\uB7C9\uC740 {0}

