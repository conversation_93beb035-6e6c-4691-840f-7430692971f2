package com.ruoyi.web.controller.bussiness;

import com.ruoyi.bussiness.domain.TMineFinancial;
import com.ruoyi.bussiness.domain.TMineFinancialPeriod;
import com.ruoyi.bussiness.service.ITMineFinancialService;
import com.ruoyi.bussiness.service.ITMineFinancialPeriodService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.TranslatorUtil;
import com.ruoyi.web.controller.common.ApiBaseController;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 理财产品Controller
 *
 * <AUTHOR>
 * @date 2023-07-17
 */
@RestController
@RequestMapping("/api/financial")
public class TMineFinancialController extends ApiBaseController
{
    @Autowired
    private ITMineFinancialService tMineFinancialService;

    @Autowired
    private ITMineFinancialPeriodService periodService;

    /**
     * 查询理财产品列表
     */
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody  TMineFinancial tMineFinancial,HttpServletRequest request)
    {
        String lang = request.getHeader("Lang");
        // 如果lang为null，默认设置为"zh"
        if (lang == null) {
            lang = "zh";
        }

        startPage();
        List<TMineFinancial> list = tMineFinancialService.selectTMineFinancialList(tMineFinancial);
        String finalLang = lang;
        list.forEach(f ->{
            if (!"zh".equals(finalLang)){
                try {
                    // 检查字段是否为null，避免NullPointerException
                    if (StringUtils.isNotEmpty(f.getTitle())) {
                        f.setTitle(TranslatorUtil.translate("zh-CN", finalLang, f.getTitle()));
                    }
                    if (StringUtils.isNotEmpty(f.getProblem())) {
                        f.setProblem(TranslatorUtil.translate("zh-CN", finalLang, f.getProblem()));
                    }
                    if (StringUtils.isNotEmpty(f.getProdectIntroduction())) {
                        f.setProdectIntroduction(TranslatorUtil.translate("zh-CN", finalLang, f.getProdectIntroduction()));
                    }
                } catch (Exception e) {
                    // 记录错误但不抛出异常，避免整个接口失败
                    e.printStackTrace();
                    System.err.println("翻译失败，使用原始文本。Lang: " + finalLang + ", Error: " + e.getMessage());
                    // 翻译失败时保持原始数据不变，不进行任何修改
                }
            }
        } );
        return getDataTable(list);
    }



    /**
     * 获取理财产品详细信息
     */
    @PostMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id,HttpServletRequest request)
    {
        String lang = request.getHeader("Lang");
        // 如果lang为null，默认设置为"zh"
        if (lang == null) {
            lang = "zh";
        }

        TMineFinancial tMineFinancial = tMineFinancialService.selectTMineFinancialById(id);

        // 只有当语言不是中文且数据不为null时才进行翻译
        if (!"zh".equals(lang) && tMineFinancial != null) {
            try {
                if (StringUtils.isNotEmpty(tMineFinancial.getTitle())) {
                    tMineFinancial.setTitle(TranslatorUtil.translate("zh-CN", lang, tMineFinancial.getTitle()));
                }
                if (StringUtils.isNotEmpty(tMineFinancial.getProblem())) {
                    tMineFinancial.setProblem(TranslatorUtil.translate("zh-CN", lang, tMineFinancial.getProblem()));
                }
                if (StringUtils.isNotEmpty(tMineFinancial.getProdectIntroduction())) {
                    tMineFinancial.setProdectIntroduction(TranslatorUtil.translate("zh-CN", lang, tMineFinancial.getProdectIntroduction()));
                }
            } catch (Exception e) {
                e.printStackTrace();
                System.err.println("翻译失败，使用原始文本。Lang: " + lang + ", Error: " + e.getMessage());
                // 翻译失败时保持原始数据不变，不进行任何修改
            }
        }
        return success(tMineFinancial);
    }

    @ApiOperation(value = "理财产品购买")
    @PostMapping("/submit")
    @Transactional
    public AjaxResult submit(Long planId, BigDecimal money, Long days) {
        String msg = tMineFinancialService.submit(planId,money,days);
        if(StringUtils.isNotBlank(msg)){
            return AjaxResult.error(msg);
        }
        return  AjaxResult.success();
    }

//    @ApiOperation(value = "理财产品赎回")
//    @PostMapping("/reCall")
//    public AjaxResult reCall(String id) {
//        String msg = tMineFinancialService.reCall(id);
//        if(StringUtils.isNotBlank(msg)){
//            return AjaxResult.error(msg);
//        }
//        return  AjaxResult.success();
//    }

    @ApiOperation(value = "个人收益类加")
    @PostMapping("/personalIncome")
    public AjaxResult personalIncome() {
        return  AjaxResult.success(tMineFinancialService.personalIncome());
    }

    @ApiOperation(value = "获取理财产品周期列表")
    @PostMapping("/periods/{financialId}")
    public AjaxResult getPeriods(@PathVariable("financialId") Long financialId) {
        List<TMineFinancialPeriod> periods = periodService.selectPeriodsByFinancialId(financialId);
        return AjaxResult.success(periods);
    }
}
