package com.ruoyi.web.controller.bussiness;

import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.bussiness.domain.TMineOrder;
import com.ruoyi.bussiness.service.ITMineFinancialService;
import com.ruoyi.bussiness.service.ITMineOrderService;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.utils.SpringContextUtil;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.quartz.task.MineFinancialTask;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.Date;
import java.util.List;

/**
 * 理财订单Controller
 *
 * <AUTHOR>
 * @date 2023-07-17
 */
@RestController
@RequestMapping("/bussiness/order")
public class TMineOrderController extends BaseController
{
    @Autowired
    private ITMineOrderService tMineOrderService;
    @Resource
    private ITMineFinancialService tMineFinancialService;

    /**
     * 查询理财订单列表
     */
    @PreAuthorize("@ss.hasPermi('bussiness:order:list')")
    @GetMapping("/list")
    public TableDataInfo list(TMineOrder tMineOrder)
    {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser user = loginUser.getUser();
        if(!user.isAdmin()){
            if (StringUtils.isNotBlank(user.getUserType()) && !user.getUserType().equals("0")){
                tMineOrder.setAdminUserIds(String.valueOf(user.getUserId()));
            }
        }
        startPage();
        List<TMineOrder> list = tMineOrderService.selectTMineOrderList(tMineOrder);
        return getDataTable(list);
    }

    /**
     * 导出理财订单列表
     */
    @PreAuthorize("@ss.hasPermi('bussiness:order:export')")
    @Log(title = "理财订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TMineOrder tMineOrder)
    {
        List<TMineOrder> list = tMineOrderService.selectTMineOrderList(tMineOrder);
        ExcelUtil<TMineOrder> util = new ExcelUtil<TMineOrder>(TMineOrder.class);
        util.exportExcel(response, list, "理财订单数据");
    }

    /**
     * 获取理财订单详细信息
     */
    @PreAuthorize("@ss.hasPermi('bussiness:order:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(tMineOrderService.selectTMineOrderById(id));
    }

    /**
     * 新增理财订单
     */
    @PreAuthorize("@ss.hasPermi('bussiness:order:add')")
    @Log(title = "理财订单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TMineOrder tMineOrder)
    {
        return toAjax(tMineOrderService.insertTMineOrder(tMineOrder));
    }

    /**
     * 修改理财订单
     */
    @PreAuthorize("@ss.hasPermi('bussiness:order:edit')")
    @Log(title = "理财订单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TMineOrder tMineOrder)
    {
        return toAjax(tMineOrderService.updateTMineOrder(tMineOrder));
    }

    /**
     * 删除理财订单
     */
    @PreAuthorize("@ss.hasPermi('bussiness:order:remove')")
    @Log(title = "理财订单", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(tMineOrderService.deleteTMineOrderByIds(ids));
    }

    @PreAuthorize("@ss.hasPermi('bussiness:order:reCall')")
    @Log(title = "理财赎回", businessType = BusinessType.UPDATE)
    @PutMapping("/reCall")
    public AjaxResult reCall(String id) {
        String msg = tMineFinancialService.reCall(id);
        if(StringUtils.isNotBlank(msg)){
            return AjaxResult.error(msg);
        }
        return  AjaxResult.success();
    }

    /**
     * 手动结算过期订单
     */
    @PreAuthorize("@ss.hasPermi('bussiness:order:edit')")
    @Log(title = "手动结算过期订单", businessType = BusinessType.UPDATE)
    @PostMapping("/manualSettlement")
    public AjaxResult manualSettlement() {
        try {
            // 调用定时任务中的手动结算方法
            // 方法1：使用SpringContextUtil（推荐）
            MineFinancialTask mineFinancialTask = (MineFinancialTask) SpringContextUtil.getBean("mineFinancialTask", MineFinancialTask.class);
            // 方法2：备用方案，如果上面有问题可以使用这个
            // MineFinancialTask mineFinancialTask = SpringUtils.getBean("mineFinancialTask");
            mineFinancialTask.manualSettlementExpiredOrders();
            return AjaxResult.success("手动结算执行成功");
        } catch (Exception e) {
            return AjaxResult.error("手动结算执行失败：" + e.getMessage());
        }
    }

    /**
     * 手动结算单个订单
     */
    @PreAuthorize("@ss.hasPermi('bussiness:order:edit')")
    @Log(title = "手动结算单个订单", businessType = BusinessType.UPDATE)
    @PostMapping("/manualSettlementSingle")
    public AjaxResult manualSettlementSingle(@RequestParam Long orderId) {
        try {
            TMineOrder order = tMineOrderService.selectTMineOrderById(orderId);
            if (order == null) {
                return AjaxResult.error("订单不存在");
            }
            if (order.getStatus() != 0) {
                return AjaxResult.error("订单状态不是收益中，无法结算");
            }

            // 调用定时任务中的单个订单结算方法
            MineFinancialTask mineFinancialTask = (MineFinancialTask) SpringContextUtil.getBean("mineFinancialTask", MineFinancialTask.class);
            mineFinancialTask.manualSettlementSingleOrder(orderId);

            return AjaxResult.success("订单结算执行成功");
        } catch (Exception e) {
            return AjaxResult.error("订单结算执行失败：" + e.getMessage());
        }
    }

    /**
     * 简化手动结算过期订单（测试用）
     */
    @PreAuthorize("@ss.hasPermi('bussiness:order:edit')")
    @Log(title = "简化手动结算过期订单", businessType = BusinessType.UPDATE)
    @PostMapping("/simpleManualSettlement")
    public AjaxResult simpleManualSettlement() {
        try {
            // 调用简化的手动结算方法
            MineFinancialTask mineFinancialTask = (MineFinancialTask) SpringContextUtil.getBean("mineFinancialTask", MineFinancialTask.class);
            mineFinancialTask.simpleManualSettlement();
            return AjaxResult.success("简化手动结算执行成功");
        } catch (Exception e) {
            return AjaxResult.error("简化手动结算执行失败：" + e.getMessage());
        }
    }

    /**
     * 检查是否有过期未结算的订单
     */
    @PreAuthorize("@ss.hasPermi('bussiness:order:list')")
    @GetMapping("/check/expired")
    public AjaxResult hasExpiredOrders() {
        try {
            // 查询过期未结算的订单数量
            long expiredCount = tMineOrderService.count(new LambdaQueryWrapper<TMineOrder>()
                    .eq(TMineOrder::getStatus, 0L)
                    .lt(TMineOrder::getEndTime, new Date()));

            return AjaxResult.success().put("hasExpired", expiredCount > 0).put("count", expiredCount);
        } catch (Exception e) {
            return AjaxResult.error("检查过期订单失败：" + e.getMessage());
        }
    }

    /**
     * 直接更新过期订单状态（最简单的方式）
     */
    @PreAuthorize("@ss.hasPermi('bussiness:order:edit')")
    @Log(title = "直接更新过期订单状态", businessType = BusinessType.UPDATE)
    @PostMapping("/directUpdateExpiredOrders")
    public AjaxResult directUpdateExpiredOrders() {
        try {
            // 直接通过Service更新过期订单状态
            List<TMineOrder> expiredOrders = tMineOrderService.list(new LambdaQueryWrapper<TMineOrder>()
                    .eq(TMineOrder::getStatus, 0L)
                    .lt(TMineOrder::getEndTime, new Date()));

            if (expiredOrders.isEmpty()) {
                return AjaxResult.success("没有找到过期未结算的订单");
            }

            int updatedCount = 0;
            for (TMineOrder order : expiredOrders) {
                order.setStatus(1L); // 设置为已结算
                order.setSettleTime(new Date()); // 设置结算时间
                int result = tMineOrderService.updateTMineOrder(order);
                if (result > 0) {
                    updatedCount++;
                }
            }

            return AjaxResult.success("成功更新 " + updatedCount + " 个过期订单状态");
        } catch (Exception e) {
            return AjaxResult.error("更新订单状态失败：" + e.getMessage());
        }
    }
}
