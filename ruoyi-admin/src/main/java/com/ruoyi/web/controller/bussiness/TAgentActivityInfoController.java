package com.ruoyi.web.controller.bussiness;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.bussiness.domain.TAgentActivityInfo;
import com.ruoyi.bussiness.service.ITAgentActivityInfoService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 返利活动明细Controller
 * 
 * <AUTHOR>
 * @date 2023-07-06
 */
@RestController
@RequestMapping("/bussiness/agentactivity")
public class TAgentActivityInfoController extends BaseController
{
    @Autowired
    private ITAgentActivityInfoService tAgentActivityInfoService;

    /**
     * 查询返利活动明细列表
     */
    @PreAuthorize("@ss.hasPermi('system:info:list')")
    @GetMapping("/list")
    public TableDataInfo list(TAgentActivityInfo tAgentActivityInfo)
    {
        startPage();
        List<TAgentActivityInfo> list = tAgentActivityInfoService.selectTAgentActivityInfoList(tAgentActivityInfo);
        return getDataTable(list);
    }

    /**
     * 导出返利活动明细列表
     */
    @PreAuthorize("@ss.hasPermi('system:info:export')")
    @Log(title = "返利活动明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TAgentActivityInfo tAgentActivityInfo)
    {
        List<TAgentActivityInfo> list = tAgentActivityInfoService.selectTAgentActivityInfoList(tAgentActivityInfo);
        ExcelUtil<TAgentActivityInfo> util = new ExcelUtil<TAgentActivityInfo>(TAgentActivityInfo.class);
        util.exportExcel(response, list, "返利活动明细数据");
    }

    /**
     * 获取返利活动明细详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:info:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(tAgentActivityInfoService.selectTAgentActivityInfoById(id));
    }

    /**
     * 新增返利活动明细
     */
    @PreAuthorize("@ss.hasPermi('system:info:add')")
    @Log(title = "返利活动明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TAgentActivityInfo tAgentActivityInfo)
    {
        return toAjax(tAgentActivityInfoService.insertTAgentActivityInfo(tAgentActivityInfo));
    }

    /**
     * 修改返利活动明细
     */
    @PreAuthorize("@ss.hasPermi('system:info:edit')")
    @Log(title = "返利活动明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TAgentActivityInfo tAgentActivityInfo)
    {
        return toAjax(tAgentActivityInfoService.updateTAgentActivityInfo(tAgentActivityInfo));
    }

    /**
     * 删除返利活动明细
     */
    @PreAuthorize("@ss.hasPermi('system:info:remove')")
    @Log(title = "返利活动明细", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(tAgentActivityInfoService.deleteTAgentActivityInfoByIds(ids));
    }
}
