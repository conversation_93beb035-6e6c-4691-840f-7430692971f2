package com.ruoyi.web.controller.bussiness;

import com.ruoyi.bussiness.domain.TVipConfig;
import com.ruoyi.bussiness.service.ITVipConfigService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

@RestController
@RequestMapping("/bussiness/vip")
public class TVipConfigController extends BaseController {

    @Autowired
    private ITVipConfigService vipConfigService;

    @PreAuthorize("@ss.hasPermi('bussiness:vip:list')")
    @GetMapping("/list")
    public TableDataInfo list(TVipConfig config) {
        startPage();
        List<TVipConfig> list = vipConfigService.selectTVipConfigList(config);
        return getDataTable(list);
    }

    @PreAuthorize("@ss.hasPermi('bussiness:vip:export')")
    @Log(title = "VIP配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TVipConfig config) {
        List<TVipConfig> list = vipConfigService.selectTVipConfigList(config);
        ExcelUtil<TVipConfig> util = new ExcelUtil<>(TVipConfig.class);
        util.exportExcel(response, list, "VIP配置数据");
    }

    @PreAuthorize("@ss.hasPermi('bussiness:vip:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(vipConfigService.selectTVipConfigById(id));
    }

    @PreAuthorize("@ss.hasPermi('bussiness:vip:add')")
    @Log(title = "VIP配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TVipConfig config) {
        return toAjax(vipConfigService.insertTVipConfig(config));
    }

    @PreAuthorize("@ss.hasPermi('bussiness:vip:edit')")
    @Log(title = "VIP配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TVipConfig config) {
        return toAjax(vipConfigService.updateTVipConfig(config));
    }

    @PreAuthorize("@ss.hasPermi('bussiness:vip:remove')")
    @Log(title = "VIP配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(vipConfigService.deleteTVipConfigByIds(ids));
    }
}





















