user.appname=\u4E2D\u6587\u8BED\u8A00



#\u9519\u8BEF\u6D88\u606F
not.null=* \u5FC5\u987B\u586B\u5199
user.jcaptcha.error=\u9A8C\u8BC1\u7801\u9519\u8BEF
user.jcaptcha.expire=\u9A8C\u8BC1\u7801\u5DF2\u5931\u6548
user.not.exists=\u7528\u6237\u4E0D\u5B58\u5728/\u5BC6\u7801\u9519\u8BEF
user.password.not.match=\u7528\u6237\u4E0D\u5B58\u5728/\u5BC6\u7801\u9519\u8BEF
user.password.retry.limit.count=\u5BC6\u7801\u8F93\u5165\u9519\u8BEF{0}\u6B21
user.password.retry.limit.exceed=\u5BC6\u7801\u8F93\u5165\u9519\u8BEF{0}\u6B21\uFF0C\u5E10\u6237\u9501\u5B9A{1}\u5206\u949F
user.password.delete=\u5BF9\u4E0D\u8D77\uFF0C\u60A8\u7684\u8D26\u53F7\u5DF2\u88AB\u5220\u9664
user.blocked=\u7528\u6237\u5DF2\u5C01\u7981\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458
role.blocked=\u89D2\u8272\u5DF2\u5C01\u7981\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458
login.blocked=\u5F88\u9057\u61BE\uFF0C\u8BBF\u95EEIP\u5DF2\u88AB\u5217\u5165\u7CFB\u7EDF\u9ED1\u540D\u5355
user.logout.success=\u9000\u51FA\u6210\u529F
user.ops.status=\u64CD\u4F5C\u6210\u529F

length.not.valid=\u957F\u5EA6\u5FC5\u987B\u5728{min}\u5230{max}\u4E2A\u5B57\u7B26\u4E4B\u95F4

user.username.not.valid=* 2\u523020\u4E2A\u6C49\u5B57\u3001\u5B57\u6BCD\u3001\u6570\u5B57\u6216\u4E0B\u5212\u7EBF\u7EC4\u6210\uFF0C\u4E14\u5FC5\u987B\u4EE5\u975E\u6570\u5B57\u5F00\u5934
user.password.not.valid=* 5-50\u4E2A\u5B57\u7B26

user.email.not.valid=\u90AE\u7BB1\u683C\u5F0F\u9519\u8BEF
user.mobile.phone.number.not.valid=\u624B\u673A\u53F7\u683C\u5F0F\u9519\u8BEF
user.login.success=\u767B\u5F55\u6210\u529F
user.register.success=\u6CE8\u518C\u6210\u529F
user.notfound=\u8BF7\u91CD\u65B0\u767B\u5F55
user.forcelogout=\u7BA1\u7406\u5458\u5F3A\u5236\u9000\u51FA\uFF0C\u8BF7\u91CD\u65B0\u767B\u5F55
user.unknown.error=\u672A\u77E5\u9519\u8BEF\uFF0C\u8BF7\u91CD\u65B0\u767B\u5F55

##\u6587\u4EF6\u4E0A\u4F20\u6D88\u606F
upload.exceed.maxSize=\u4E0A\u4F20\u7684\u6587\u4EF6\u5927\u5C0F\u8D85\u51FA\u9650\u5236\u7684\u6587\u4EF6\u5927\u5C0F\uFF01<br/>\u5141\u8BB8\u7684\u6587\u4EF6\u6700\u5927\u5927\u5C0F\u662F\uFF1A{0}MB\uFF01
upload.filename.exceed.length=\u4E0A\u4F20\u7684\u6587\u4EF6\u540D\u6700\u957F{0}\u4E2A\u5B57\u7B26

##\u6743\u9650
no.permission=\u60A8\u6CA1\u6709\u6570\u636E\u7684\u6743\u9650\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\u6DFB\u52A0\u6743\u9650 [{0}]
no.create.permission=\u60A8\u6CA1\u6709\u521B\u5EFA\u6570\u636E\u7684\u6743\u9650\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\u6DFB\u52A0\u6743\u9650 [{0}]
no.update.permission=\u60A8\u6CA1\u6709\u4FEE\u6539\u6570\u636E\u7684\u6743\u9650\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\u6DFB\u52A0\u6743\u9650 [{0}]
no.delete.permission=\u60A8\u6CA1\u6709\u5220\u9664\u6570\u636E\u7684\u6743\u9650\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\u6DFB\u52A0\u6743\u9650 [{0}]
no.export.permission=\u60A8\u6CA1\u6709\u5BFC\u51FA\u6570\u636E\u7684\u6743\u9650\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\u6DFB\u52A0\u6743\u9650 [{0}]
no.view.permission=\u60A8\u6CA1\u6709\u67E5\u770B\u6570\u636E\u7684\u6743\u9650\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\u6DFB\u52A0\u6743\u9650 [{0}]

##\u6CE8\u518C\u7528\u6237\u63D0\u793A
user.register.email.format=\u90AE\u7BB1\u683C\u5F0F\u4E0D\u6B63\u786E
user.register.email.exisit=\u90AE\u7BB1\u5DF2\u5B58\u5728
user.register.phone.exisit=\u90AE\u7BB1\u5DF2\u5B58\u5728
user.user_name_exisit=\u7528\u6237\u540D\u5DF2\u7ECF\u5B58\u5728
login.user_error=\u7528\u6237\u540D\u6216\u5BC6\u7801\u9519\u8BEF
user.login.address.error=\u5730\u5740\u88AB\u5360\u7528!
#app
app.login.address.not.null=\u7528\u6237\u5730\u5740\u4E3A\u7A7A
login.email.not_register=\u8BF7\u4F7F\u7528\u5DF2\u7ED1\u5B9A\u90AE\u7BB1\u8FDB\u884C\u64CD\u4F5C
login.phone.not_register=\u8BF7\u4F7F\u7528\u5DF2\u7ED1\u5B9A\u624B\u673A\u53F7\u8FDB\u884C\u64CD\u4F5C
login.code_error=\u9A8C\u8BC1\u7801\u9519\u8BEF
user.login.code.error=\u9A8C\u8BC1\u7801\u6548\u9A8C\u5931\u8D25!
user.login.password.null=\u8BF7\u8F93\u5165\u5BC6\u7801!
user.login.upd.success=\u4FEE\u6539\u6210\u529F!
phone_code_empty=\u624B\u673A\u53F7\u4E0D\u80FD\u4E3A\u7A7A!
email.code_empty=\u90AE\u7BB1\u4E0D\u80FD\u4E3A\u7A7A!
user.code.send=\u9A8C\u8BC1\u7801\u53D1\u9001\u6210\u529F
app.verification.email.code=\u9A8C\u8BC1\u7801\u5DF2\u53D1\u9001\u5230\u60A8\u7684\u90AE\u7BB1\uFF0C\u8BF7\u6CE8\u610F\u67E5\u6536
user.password_bind=\u5BC6\u7801\u5DF2\u7ECF\u8BBE\u7F6E\uFF0C\u8BF7\u52FF\u91CD\u590D\u7ED1\u5B9A
user.tard.password_bind=\u5B89\u5168\u5BC6\u7801\u5DF2\u7ECF\u8BBE\u7F6E\uFF0C\u8BF7\u52FF\u91CD\u590D\u7ED1\u5B9A
user.login.null=\u7528\u6237\u4E0D\u5B58\u5728\uFF01
user.login.old.password=\u65E7\u5BC6\u7801\u672A\u8F93\u5165\uFF01
user.login.new.password=\u65B0\u5BC6\u7801\u672A\u8F93\u5165\uFF01
user.login.paw.upd=\u65B0\u5BC6\u7801\u4E0D\u80FD\u4E0E\u65E7\u5BC6\u7801\u4E00\u81F4\uFF01
user.login.old.password.error=\u65E7\u5BC6\u7801\u9519\u8BEF\uFF01
user.login.address.null=\u5730\u5740\u4E3A\u7A7A\uFF01
user.login.userid.null=\u7528\u6237ID\u4E3A\u7A7A\uFF01
#\u63D0\u73B0
user.password_notbind=\u8BF7\u8BBE\u7F6E\u5B89\u5168\u5BC6\u7801
tard_password.error=\u652F\u4ED8\u5BC6\u7801\u9519\u8BEF
withdraw.amount_number_exceed=\u5F53\u65E5\u63D0\u73B0\u8D85\u8FC7\u8BBE\u5B9A\u6B21\u6570
withdraw_error=\u4F59\u989D\u4E0D\u8DB3\uFF0C\u65E0\u6CD5\u63D0\u73B0
withdraw.amount_error=\u63D0\u73B0\u91D1\u989D\u9519\u8BEF\uFF0C\u8BF7\u4FEE\u6539
withdraw.refresh=\u8BF7\u5237\u65B0
withdraw.address.isnull=\u8BF7\u586B\u5199\u6B63\u786E\u7684\u63D0\u73B0\u5730\u5740\uFF01
#\u5B9E\u540D\u8BA4\u8BC1
user.kyc.not_blank=\u8EAB\u4EFD\u8BA4\u8BC1\u4FE1\u606F4\u9879\u90FD\u662F\u5FC5\u586B

#\u5151\u6362
exchange.symbol.exist = \u5151\u6362\u7684\u5E01\u79CD\u4E0D\u80FD\u4E3A\u7A7A,\u5151\u6362\u91D1\u989D\u5FC5\u987B\u5927\u4E8E0
recharge.amout.min=\u6700\u5C0F\u5145\u503C\u91D1\u989D\u4E3A{0}
recharge.amout.max=\u6700\u5927\u5145\u503C\u91D1\u989D\u4E3A{0}
currency.exchange_min = \u6700\u5C0F\u5151\u6362\u91D1\u989D\u4E3A{0}
currency.exchange_max = \u6700\u5927\u5151\u6362\u91D1\u989D\u4E3A{0}
exchange_error=\u5151\u6362\u91D1\u989D\u5927\u4E8E\u8D26\u6237\u4F59\u989D
exchange.record.exist.error=\u60A8\u6709\u4E00\u7B14\u5151\u6362\u6B63\u5728\u8FDB\u884C\u4E2D\uFF0C\u7A0D\u540E\u518D\u8BD5
exchange_symbol_error_exist = \u5151\u6362\u5E01\u79CD\u4E0D\u5B58\u5728
#\u79D2\u5408\u7EA6
order_amount_error=\u94B1\u5305\u4F59\u989D\u4E0D\u8DB3\uFF0C\u65E0\u6CD5\u8D2D\u4E70
order_10s_retry=\u4E0B\u5355\u8FC7\u4E8E\u9891\u7E41\uFF0C\u8BF710s\u540E\u5C1D\u8BD5\u4E0B\u5355...
#\u7406\u8D22
user.push.message=\u7528\u6237\u64CD\u4F5C\u88AB\u7981\u6B62
mine.level.error=\u60A8\u7684VIP\u7B49\u7EA7\u4E0D\u591F\uFF0C\u65E0\u6CD5\u8D2D\u4E70\u6B21\u7406\u8D22\u4EA7\u54C1
order.single.min=\u8D2D\u4E70\u7684\u91D1\u989D\u5C11\u4E8E\u6700\u4F4E\u9650\u989D\uFF0C\u65E0\u6CD5\u8D2D\u4E70
order.single.max=\u8D2D\u4E70\u7684\u91D1\u989D\u5927\u4E8E\u6700\u5927\u9650\u989D\uFF0C\u65E0\u6CD5\u8D2D\u4E70
order.buy.insufficient=\u60A8\u5F53\u524D\u6700\u591A\u53EF\u8D2D\u4E70\u91D1\u989D\u4E3A{0}
product.removed=\u4EA7\u54C1\u5DF2\u4E0B\u67B6
financial.count.max=\u6B64\u7528\u6237\u5DF2\u5230\u8FBE\u9650\u5236\u6B21\u6570,\u4E0D\u5141\u8BB8\u8D2D\u4E70
days.not.null=\u8D2D\u4E70\u5468\u671F\u4E3A\u7A7A
days.not.error=\u8D2D\u4E70\u5468\u671F\u9519\u8BEF

contract.accont.error=\u5408\u7EA6\u8D26\u6237\u4F59\u989D\u4E0D\u8DB3
contract.min.share=\u6700\u4F4E\u8D2D\u4E70\u6570\u4E3A{0}
contract.max.share=\u6700\u9AD8\u8D2D\u4E70\u6570\u4E3A{0}
contract.asset.error=\u5408\u7EA6\u8D26\u6237\u4F59\u989D\u4E0D\u8DB3
adjust.min.error=\u51CF\u5C11\u7684\u4FDD\u8BC1\u91D1\u4E0D\u80FD\u5C0F\u4E8E\u521D\u59CB\u4FDD\u8BC1\u91D1  
order.status.error=\u975E\u6CD5\u63D0\u4EA4
contract.buy.earn.error=\u4E70\u5165\u505A\u591A\uFF0C\u6B62\u76C8\u4EF7\u4E0D\u80FD\u5C0F\u4E8E\u5F00\u4ED3\u5747\u4EF7
contract.buy.loss.error=\u4E70\u5165\u505A\u591A\uFF0C\u6B62\u635F\u4EF7\u4E0D\u80FD\u5927\u4E8E\u5F00\u4ED3\u5747\u4EF7
contract.sell.earn.error=\u5356\u51FA\u5F00\u7A7A\uFF0C\u6B62\u76C8\u4EF7\u4E0D\u80FD\u5927\u4E8E\u5F00\u4ED3\u5747\u4EF7
contract.sell.loss.error=\u5356\u51FA\u5F00\u7A7A,\u6B62\u635F\u4EF7\u4E0D\u80FD\u5C0F\u4E8E\u5F00\u4ED3\u5747\u4EF7
contract.num.limit=\u6570\u91CF\u4E0D\u80FD\u5927\u4E8E\u6301\u4ED3\u91CF
#\u8D44\u91D1\u5212\u8F6C
asset_amount_error=\u4F59\u989D\u4E0D\u8DB3

order.amount_error=\u7406\u8D22\u8D26\u6237\u4F59\u989D\u4E0D\u8DB3

order.10s_retry=\u4E0B\u5355\u8FC7\u4E8E\u9891\u7E41\uFF0C\u8BF710s\u540E\u5C1D\u8BD5\u4E0B\u5355...

back.code.exist.error = \u94F6\u884C\u5361\u53F7\u5DF2\u7ECF\u5B58\u5728{}



contract.delivery.day=\u8DDD\u79BB\u4EA4\u5272\u65F6\u95F4\u8FD8\u6709\uFF1A{0}\u5929\u3002
contract.delivery.margan=\u8DDD\u79BB\u4EA4\u5272\u65F6\u95F4\u8FD8\u6709\uFF1A{0}\u5929\uFF1B\u5982\u60A8\u7684\u6301\u4ED3\u62C5\u4FDD\u8D44\u4EA7\u5927\u4E8E\u7B49\u4E8E{1} USDT \u6216\u8005\u5408\u7EA6\u8D44\u4EA7\u5C0F\u4E8E\u7B49\u4E8E{2}USDT,\u53EF\u4EE5\u63D0\u524D\u5E73\u4ED3\u3002
contract.delivery.earn=\u8DDD\u79BB\u4EA4\u5272\u65F6\u95F4\u8FD8\u6709\uFF1A{0}\u5929\uFF1B\u5982\u60A8\u7684\u6301\u4ED3\u62C5\u4FDD\u8D44\u4EA7\u5927\u4E8E\u7B49\u4E8E{1} USDT  \u53EF\u4EE5\u63D0\u524D\u5E73\u4ED3\u3002
contract.delivery.loss=\u8DDD\u79BB\u4EA4\u5272\u65F6\u95F4\u8FD8\u6709\uFF1A{0}\u5929\uFF1B\u5982\u60A8\u7684\u6301\u4ED3\u62C5\u4FDD\u8D44\u4EA7\u5C0F\u4E8E\u7B49\u4E8E{1}USDT,\u53EF\u4EE5\u63D0\u524D\u5E73\u4ED3\u3002
order.audit.error=\u6B63\u5728\u5BA1\u6838\u4E2D
order.audit.pass=\u6B63\u5728\u5145\u503C\u4E2D\uFF0C\u9700\u8981\u5168\u74035\u4E2A\u8282\u70B9\u786E\u8BA4\uFF0C\u8BF7\u60A8\u8010\u5FC3\u7B49\u5F85

#\u7533\u8D2D
own.coin.limit.num= \u53EF\u8D2D\u4E70\u6570\u91CF{0}
own.coin.limit= \u53EF\u7528\u4F59\u989D{0}
own.coin.success= \u7533\u8D2D\u6210\u529F
own.coin.error= \u7CFB\u7EDF\u9519\u8BEF\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458
own.coin.sub.play=\u60A8\u5DF2\u7533\u8D2D\uFF0C\u8BF7\u52FF\u91CD\u590D\u63D0\u4EA4\uFF01
own.coin.sub.error=\u672A\u8BA2\u9605\u6210\u529F\u4E0D\u53EF\u7533\u8D2D
own.coin.sub.success=\u60A8\u5DF2\u83B7\u5F97 {0} \u7533\u8D2D\u8D44\u683C
own.coin.sub.num.error=\u8BF7\u586B\u5199\u6B63\u786E\u6570\u91CF
order.sell.min.error=\u6700\u4F4E\u5356\u51FA\u91CF\u4E3A{0}
order.audit.reject=\u5E73\u4ED3\u5931\u8D25

#\u65B0\u53D1\u5E01\u8BA2\u9605
own.coin.subscribe.success=\u8BA2\u9605\u6210\u529F\uFF01
own.coin.subscribe.error=\u8BA2\u9605\u5931\u8D25\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5\uFF01