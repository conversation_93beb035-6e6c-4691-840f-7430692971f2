<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0" 
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
      xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 http://maven.apache.org/xsd/settings-1.0.0.xsd">
<localRepository>D:\repository</localRepository>
<pluginGroups>
<pluginGroup>org.mortbay.jetty</pluginGroup>
</pluginGroups>
<proxies>
</proxies>
<servers>
 <server>
    <id>echo2-group</id>
    <username>admin</username>
    <password>admin123</password>
  </server>
  <server>
    <id>Snapshots</id>
    <username>ali</username>
    <password>ali</password>
  </server>
</servers>
<mirrors>
 <mirror>
  <!--This sends everything else to /public -->
  <id>echo2-group</id>
  <mirrorOf>*</mirrorOf> 
  <url>https://nexus.ok6.cc/repository/echo2-group/</url>
 </mirror>
 <mirror>
  <!--This is used to direct the public snapshots repo in the 
      profile below over to a different nexus group -->
  <id>nexus-public-snapshots</id>
  <mirrorOf>public-snapshots</mirrorOf> 
  <url>http://maven.aliyun.com/nexus/content/repositories/snapshots/</url>
 </mirror>
</mirrors>
<profiles> 
 <profile>
  <id>development</id>
  <repositories>
    <repository>
      <id>central</id>
      <url>http://central</url>
      <releases><enabled>true</enabled><updatePolicy>always</updatePolicy></releases>
      <snapshots><enabled>true</enabled><updatePolicy>always</updatePolicy></snapshots>
    </repository>
  </repositories>
 <pluginRepositories>
    <pluginRepository>
      <id>central</id>
      <url>http://central</url>
      <releases><enabled>true</enabled><updatePolicy>always</updatePolicy></releases>
      <snapshots><enabled>true</enabled><updatePolicy>always</updatePolicy></snapshots>
    </pluginRepository>
  </pluginRepositories>
 </profile>
 <profile>
  <!--this profile will allow snapshots to be searched when activated-->
  <id>public-snapshots</id>
  <repositories>
    <repository>
      <id>public-snapshots</id>
      <url>http://public-snapshots</url>
      <releases><enabled>false</enabled></releases>
      <snapshots><enabled>true</enabled><updatePolicy>always</updatePolicy></snapshots>
    </repository>
  </repositories>
  <pluginRepositories>
    <pluginRepository>
      <id>public-snapshots</id>
      <url>http://public-snapshots</url>
      <releases><enabled>false</enabled></releases>
      <snapshots><enabled>true</enabled><updatePolicy>always</updatePolicy></snapshots>
    </pluginRepository>
   </pluginRepositories>
  </profile>
 </profiles> 
 <activeProfiles>
  <activeProfile>development</activeProfile>
  <activeProfile>public-snapshots</activeProfile>
 </activeProfiles>
</settings>