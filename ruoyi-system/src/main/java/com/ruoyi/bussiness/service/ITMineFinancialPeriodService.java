package com.ruoyi.bussiness.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.bussiness.domain.TMineFinancialPeriod;
import java.util.List;

/**
 * 理财产品周期Service接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface ITMineFinancialPeriodService extends IService<TMineFinancialPeriod> {

    /**
     * 查询理财产品周期
     *
     * @param id 理财产品周期主键
     * @return 理财产品周期
     */
    public TMineFinancialPeriod selectTMineFinancialPeriodById(Long id);

    /**
     * 查询理财产品周期列表
     *
     * @param tMineFinancialPeriod 理财产品周期
     * @return 理财产品周期集合
     */
    public List<TMineFinancialPeriod> selectTMineFinancialPeriodList(TMineFinancialPeriod tMineFinancialPeriod);

    /**
     * 根据理财产品ID查询周期列表
     *
     * @param financialId 理财产品ID
     * @return 周期列表
     */
    public List<TMineFinancialPeriod> selectPeriodsByFinancialId(Long financialId);

    /**
     * 新增理财产品周期
     *
     * @param tMineFinancialPeriod 理财产品周期
     * @return 结果
     */
    public int insertTMineFinancialPeriod(TMineFinancialPeriod tMineFinancialPeriod);

    /**
     * 修改理财产品周期
     *
     * @param tMineFinancialPeriod 理财产品周期
     * @return 结果
     */
    public int updateTMineFinancialPeriod(TMineFinancialPeriod tMineFinancialPeriod);

    /**
     * 批量删除理财产品周期
     *
     * @param ids 需要删除的理财产品周期主键集合
     * @return 结果
     */
    public int deleteTMineFinancialPeriodByIds(Long[] ids);

    /**
     * 删除理财产品周期信息
     *
     * @param id 理财产品周期主键
     * @return 结果
     */
    public int deleteTMineFinancialPeriodById(Long id);

    /**
     * 根据理财产品ID删除所有周期
     *
     * @param financialId 理财产品ID
     * @return 结果
     */
    public int deletePeriodsByFinancialId(Long financialId);

    /**
     * 批量保存理财产品周期
     *
     * @param financialId 理财产品ID
     * @param periods 周期列表
     * @return 结果
     */
    public boolean savePeriods(Long financialId, List<TMineFinancialPeriod> periods);
}
