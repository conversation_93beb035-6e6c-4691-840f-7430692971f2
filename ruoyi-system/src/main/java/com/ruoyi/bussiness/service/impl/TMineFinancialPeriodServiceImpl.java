package com.ruoyi.bussiness.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.bussiness.domain.TMineFinancialPeriod;
import com.ruoyi.bussiness.mapper.TMineFinancialPeriodMapper;
import com.ruoyi.bussiness.service.ITMineFinancialPeriodService;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 理财产品周期Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class TMineFinancialPeriodServiceImpl extends ServiceImpl<TMineFinancialPeriodMapper, TMineFinancialPeriod> implements ITMineFinancialPeriodService {

    @Autowired
    private TMineFinancialPeriodMapper tMineFinancialPeriodMapper;

    /**
     * 查询理财产品周期
     *
     * @param id 理财产品周期主键
     * @return 理财产品周期
     */
    @Override
    public TMineFinancialPeriod selectTMineFinancialPeriodById(Long id) {
        return tMineFinancialPeriodMapper.selectTMineFinancialPeriodById(id);
    }

    /**
     * 查询理财产品周期列表
     *
     * @param tMineFinancialPeriod 理财产品周期
     * @return 理财产品周期
     */
    @Override
    public List<TMineFinancialPeriod> selectTMineFinancialPeriodList(TMineFinancialPeriod tMineFinancialPeriod) {
        return tMineFinancialPeriodMapper.selectTMineFinancialPeriodList(tMineFinancialPeriod);
    }

    /**
     * 根据理财产品ID查询周期列表
     *
     * @param financialId 理财产品ID
     * @return 周期列表
     */
    @Override
    public List<TMineFinancialPeriod> selectPeriodsByFinancialId(Long financialId) {
        return tMineFinancialPeriodMapper.selectPeriodsByFinancialId(financialId);
    }

    /**
     * 新增理财产品周期
     *
     * @param tMineFinancialPeriod 理财产品周期
     * @return 结果
     */
    @Override
    public int insertTMineFinancialPeriod(TMineFinancialPeriod tMineFinancialPeriod) {
        tMineFinancialPeriod.setCreateTime(DateUtils.getNowDate());
        return tMineFinancialPeriodMapper.insertTMineFinancialPeriod(tMineFinancialPeriod);
    }

    /**
     * 修改理财产品周期
     *
     * @param tMineFinancialPeriod 理财产品周期
     * @return 结果
     */
    @Override
    public int updateTMineFinancialPeriod(TMineFinancialPeriod tMineFinancialPeriod) {
        tMineFinancialPeriod.setUpdateTime(DateUtils.getNowDate());
        return tMineFinancialPeriodMapper.updateTMineFinancialPeriod(tMineFinancialPeriod);
    }

    /**
     * 批量删除理财产品周期
     *
     * @param ids 需要删除的理财产品周期主键
     * @return 结果
     */
    @Override
    public int deleteTMineFinancialPeriodByIds(Long[] ids) {
        return tMineFinancialPeriodMapper.deleteTMineFinancialPeriodByIds(ids);
    }

    /**
     * 删除理财产品周期信息
     *
     * @param id 理财产品周期主键
     * @return 结果
     */
    @Override
    public int deleteTMineFinancialPeriodById(Long id) {
        return tMineFinancialPeriodMapper.deleteTMineFinancialPeriodById(id);
    }

    /**
     * 根据理财产品ID删除所有周期
     *
     * @param financialId 理财产品ID
     * @return 结果
     */
    @Override
    public int deletePeriodsByFinancialId(Long financialId) {
        return tMineFinancialPeriodMapper.deletePeriodsByFinancialId(financialId);
    }

    /**
     * 批量保存理财产品周期
     *
     * @param financialId 理财产品ID
     * @param periods 周期列表
     * @return 结果
     */
    @Override
    @Transactional
    public boolean savePeriods(Long financialId, List<TMineFinancialPeriod> periods) {
        // 先删除原有的周期
        deletePeriodsByFinancialId(financialId);
        
        // 保存新的周期
        if (periods != null && !periods.isEmpty()) {
            for (int i = 0; i < periods.size(); i++) {
                TMineFinancialPeriod period = periods.get(i);
                period.setFinancialId(financialId);
                period.setSort(i + 1); // 设置排序
                period.setStatus(1); // 默认启用
                insertTMineFinancialPeriod(period);
            }
        }
        return true;
    }
}
