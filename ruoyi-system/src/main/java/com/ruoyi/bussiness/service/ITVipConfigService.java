package com.ruoyi.bussiness.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.bussiness.domain.TVipConfig;

import java.math.BigDecimal;
import java.util.List;

public interface ITVipConfigService extends IService<TVipConfig> {

    TVipConfig selectTVipConfigById(Long id);

    List<TVipConfig> selectTVipConfigList(TVipConfig config);

    int insertTVipConfig(TVipConfig config);

    int updateTVipConfig(TVipConfig config);

    int deleteTVipConfigByIds(Long[] ids);

    int deleteTVipConfigById(Long id);

    TVipConfig matchLevelByAmount(BigDecimal amount);

    TVipConfig selectByLevel(Integer level);
}



