package com.ruoyi.bussiness.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * VIP 等级配置表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_vip_config")
public class TVipConfig extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * VIP 等级 (1..6)
     */
    @Excel(name = "VIP等级")
    private Integer level;

    /**
     * 等级名称，如 V1、V2
     */
    @Excel(name = "等级名称")
    private String levelName;

    /**
     * 升级所需成长值 (USDT/USDC 1:1)
     */
    @Excel(name = "成长值", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal requiredRange;
}
















