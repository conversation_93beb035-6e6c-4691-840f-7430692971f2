package com.ruoyi.bussiness.domain.setting;

import lombok.Data;

@Data
public class VipLevelSetting {

    /**
     * 开关
     */
    private Boolean isOpen;


    /**
     * vip0 开始
     */
    private String vip0Start;
    /**
     * vip0 结束
     */
    private String vip0End;

    /**
     * vip1 开始
     */
    private String vip1Start;
    /**
     * vip1 结束
     */
    private String vip1End;

    /**
     * vip2 开始
     */
    private String vip2Start;
    /**
     * vip2 结束
     */
    private String vip2End;

    /**
     * vip3 开始
     */
    private String vip3Start;
    /**
     * vip3 结束
     */
    private String vip3End;

    /**
     * vip4 开始
     */
    private String vip4Start;
    /**
     * vip4 结束
     */
    private String vip4End;

    /**
     * vip5 开始
     */
    private String vip5Start;
    /**
     * vip5 结束
     */
    private String vip5End;

    /**
     * vip6 开始
     */
    private String vip6Start;
    /**
     * vip6 结束
     */
    private String vip6End;

    /**
     * vip7开始
     */
    private String vip7Start;
    /**
     * vip7 结束
     */
    private String vip7End;

    /**
     * vip8 开始
     */
    private String vip8Start;


}
