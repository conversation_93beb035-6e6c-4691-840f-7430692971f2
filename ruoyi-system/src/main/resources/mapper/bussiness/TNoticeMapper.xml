<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.bussiness.mapper.TNoticeMapper">
    
    <resultMap type="com.ruoyi.bussiness.domain.TNotice" id="TNoticeResult">
        <result property="noticeId"    column="notice_id"    />
        <result property="noticeTitle"    column="notice_title"    />
        <result property="noticeType"    column="notice_type"    />
        <result property="modelType"    column="model_type"    />
        <result property="noticeContent"    column="notice_content"    />
        <result property="commentsNum"    column="comments_num"    />
        <result property="cover"    column="cover"    />
        <result property="viewNum"    column="view_num"    />
        <result property="expireTime"    column="expire_time"    />
        <result property="imgUrl"    column="img_url"    />
        <result property="chainedUrl"    column="chained_url"    />
        <result property="detailUrl"    column="detail_url"    />
        <result property="languageId"    column="language_id"    />
        <result property="status"    column="status"    />
        <result property="sort"    column="sort"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="source"    column="source"    />
    </resultMap>

    <sql id="selectTNoticeVo">
        select notice_id, notice_title, notice_type, model_type, notice_content, comments_num, cover, view_num, expire_time, img_url, chained_url, detail_url, language_id, status, sort, remark, create_by, create_time, update_by, update_time, source from t_notice
    </sql>

    <select id="selectTNoticeList" parameterType="TNotice" resultMap="TNoticeResult">
        <include refid="selectTNoticeVo"/>
        <where>  
            <if test="noticeId != null  and noticeId != ''"> and notice_id =#{noticeId} </if>
            <if test="noticeTitle != null  and noticeTitle != ''"> and notice_title like concat('%',#{noticeTitle},'%') </if>
            <if test="noticeType != null  and noticeType != ''"> and notice_type = #{noticeType}</if>
            <if test="modelType != null  and modelType != ''"> and model_type = #{modelType}</if>
            <if test="noticeContent != null  and noticeContent != ''"> and notice_content = #{noticeContent}</if>
            <if test="commentsNum != null "> and comments_num = #{commentsNum}</if>
            <if test="cover != null  and cover != ''"> and cover = #{cover}</if>
            <if test="viewNum != null "> and view_num = #{viewNum}</if>
            <if test="expireTime != null "> and expire_time = #{expireTime}</if>
            <if test="imgUrl != null  and imgUrl != ''"> and img_url = #{imgUrl}</if>
            <if test="chainedUrl != null  and chainedUrl != ''"> and chained_url = #{chainedUrl}</if>
            <if test="detailUrl != null  and detailUrl != ''"> and detail_url = #{detailUrl}</if>
            <if test="languageId != null "> and language_id = #{languageId}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="sort != null "> and sort = #{sort}</if>
            <if test="source != null  and source != ''"> and source = #{source}</if>
        </where>
    </select>
    
    <select id="selectTNoticeByNoticeId" parameterType="Long" resultMap="TNoticeResult">
        <include refid="selectTNoticeVo"/>
        where notice_id = #{noticeId}
    </select>
        
    <insert id="insertTNotice" parameterType="TNotice" useGeneratedKeys="true" keyProperty="noticeId">
        insert into t_notice
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="noticeTitle != null and noticeTitle != ''">notice_title,</if>
            <if test="noticeType != null">notice_type,</if>
            <if test="modelType != null and modelType != ''">model_type,</if>
            <if test="noticeContent != null">notice_content,</if>
            <if test="commentsNum != null">comments_num,</if>
            <if test="cover != null">cover,</if>
            <if test="viewNum != null">view_num,</if>
            <if test="expireTime != null">expire_time,</if>
            <if test="imgUrl != null">img_url,</if>
            <if test="chainedUrl != null">chained_url,</if>
            <if test="detailUrl != null">detail_url,</if>
            <if test="languageId != null">language_id,</if>
            <if test="status != null">status,</if>
            <if test="sort != null">sort,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="source != null">source,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="noticeTitle != null and noticeTitle != ''">#{noticeTitle},</if>
            <if test="noticeType != null">#{noticeType},</if>
            <if test="modelType != null and modelType != ''">#{modelType},</if>
            <if test="noticeContent != null">#{noticeContent},</if>
            <if test="commentsNum != null">#{commentsNum},</if>
            <if test="cover != null">#{cover},</if>
            <if test="viewNum != null">#{viewNum},</if>
            <if test="expireTime != null">#{expireTime},</if>
            <if test="imgUrl != null">#{imgUrl},</if>
            <if test="chainedUrl != null">#{chainedUrl},</if>
            <if test="detailUrl != null">#{detailUrl},</if>
            <if test="languageId != null">#{languageId},</if>
            <if test="status != null">#{status},</if>
            <if test="sort != null">#{sort},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="source != null">#{source},</if>
         </trim>
    </insert>

    <update id="updateTNotice" parameterType="TNotice">
        update t_notice
        <trim prefix="SET" suffixOverrides=",">
            <if test="noticeTitle != null and noticeTitle != ''">notice_title = #{noticeTitle},</if>
            <if test="noticeType != null">notice_type = #{noticeType},</if>
            <if test="modelType != null and modelType != ''">model_type = #{modelType},</if>
            <if test="noticeContent != null">notice_content = #{noticeContent},</if>
            <if test="commentsNum != null">comments_num = #{commentsNum},</if>
            <if test="cover != null">cover = #{cover},</if>
            <if test="viewNum != null">view_num = #{viewNum},</if>
            <if test="expireTime != null">expire_time = #{expireTime},</if>
            <if test="imgUrl != null">img_url = #{imgUrl},</if>
            <if test="chainedUrl != null">chained_url = #{chainedUrl},</if>
            <if test="detailUrl != null">detail_url = #{detailUrl},</if>
            <if test="languageId != null">language_id = #{languageId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="source != null">source = #{source},</if>
        </trim>
        where notice_id = #{noticeId}
    </update>

    <delete id="deleteTNoticeByNoticeId" parameterType="Long">
        delete from t_notice where notice_id = #{noticeId}
    </delete>

    <delete id="deleteTNoticeByNoticeIds" parameterType="String">
        delete from t_notice where notice_id in 
        <foreach item="noticeId" collection="array" open="(" separator="," close=")">
            #{noticeId}
        </foreach>
    </delete>
</mapper>