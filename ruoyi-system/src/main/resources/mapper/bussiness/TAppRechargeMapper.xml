<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.bussiness.mapper.TAppRechargeMapper">

    <resultMap type="com.ruoyi.bussiness.domain.TAppRecharge" id="TAppRechargeResult">
        <result property="id" column="id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="userId" column="user_id"/>
        <result property="username" column="username"/>
        <result property="amount" column="amount"/>
        <result property="bonus" column="bonus"/>
        <result property="status" column="status"/>
        <result property="serialId" column="serial_id"/>
        <result property="txId" column="tx_id"/>
        <result property="type" column="type"/>
        <result property="searchValue" column="search_value"/>
        <result property="address" column="address"/>
        <result property="appParentIds" column="app_parent_ids"/>
        <result property="adminParentIds" column="admin_parent_ids"/>
        <result property="coin" column="coin"/>
        <result property="toAddress" column="to_address"/>
        <result property="blockTime" column="block_time"/>
        <result property="host" column="host"/>
        <result property="realAmount" column="real_amount"/>
        <result property="fileName" column="file_name"/>
        <result property="rechargeRemark" column="recharge_remark"/>
        <result property="noticeFlag" column="notice_flag"/>
        <result property="orderType" column="order_type"/>
    </resultMap>

    <sql id="selectTAppRechargeVo">
        select id, create_by, create_time, update_by, update_time, remark, user_id, username, amount, bonus,
        status, serial_id, tx_id, type, search_value, address, app_parent_ids, admin_parent_ids, coin, to_address,
        block_time, host,
        real_amount, file_name, recharge_remark, notice_flag,order_type from t_own_recharge
    </sql>

    <select id="selectTAppRechargeList" parameterType="TAppRecharge" resultMap="TAppRechargeResult">
        <include refid="selectTAppRechargeVo"/>
        <where>
            <if test="userId != null ">and user_id = #{userId}</if>
            <if test="username != null  and username != ''">and username like concat('%', #{username}, '%')</if>
            <if test="amount != null ">and amount = #{amount}</if>
            <if test="bonus != null ">and bonus = #{bonus}</if>
            <if test="status != null ">and status = #{status}</if>
            <if test="serialId != null  and serialId != ''">and serial_id = #{serialId}</if>
            <if test="txId != null  and txId != ''">and tx_id = #{txId}</if>
            <if test="type != null  and type != ''">and type = #{type}</if>
            <if test="searchValue != null  and searchValue != ''">and search_value = #{searchValue}</if>
            <if test="address != null  and address != ''">and address = #{address}</if>
            <if test="appParentIds != null  and appParentIds != ''">and app_parent_ids = #{appParentIds}</if>
            <if test="adminParentIds != null and adminParentIds != '' ">and admin_parent_ids = #{adminParentIds}</if>
            <if test="coin != null  and coin != ''">and coin = #{coin}</if>
            <if test="toAddress != null  and toAddress != ''">and to_address = #{toAddress}</if>
            <if test="blockTime != null ">and block_time = #{blockTime}</if>
            <if test="host != null  and host != ''">and host = #{host}</if>
            <if test="realAmount != null ">and real_amount = #{realAmount}</if>
            <if test="fileName != null  and fileName != ''">and file_name like concat('%', #{fileName}, '%')</if>
            <if test="rechargeRemark != null  and rechargeRemark != ''">and recharge_remark = #{rechargeRemark}</if>
            <if test="noticeFlag != null ">and notice_flag = #{noticeFlag}</if>
            <if test="orderType != null ">and order_type = #{orderType}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectTAppRechargeById" parameterType="Long" resultMap="TAppRechargeResult">
        <include refid="selectTAppRechargeVo"/>
        where id = #{id}
    </select>
    <select id="selectRechargeList" resultType="com.ruoyi.bussiness.domain.TAppRecharge"
            parameterType="com.ruoyi.bussiness.domain.TAppRecharge">

        SELECT
        a.id,
        a.serial_id as serialId,
        a.tx_id as txId,
        a.user_id as userId,
        a.username,
        a.amount,
        a.bonus,
        a.remark,
        a.type,
        a.status,
        a.update_time as updateTime,
        a.update_by as updateBy,
        a.create_time as createTime,
        a.tree,
        a.real_amount as realAmount,
        a.block_time as blockTime,
        a.address,
        a.file_name as fileName,
        a.`host`,
        a.to_address as toAddress,
        a.coin,
        u.is_test as isTest,
        a.recharge_remark as rechargeRemark,
        a.order_type as orderType,
        r.u_amount as uamount
        FROM
        t_own_recharge a
        LEFT JOIN t_app_user u ON a.user_id = u.user_id
        left join t_app_wallet_record r on r.serial_id = a.serial_id and (r.type = '1' or r.type = '3')
        <where>
            <if test="remark ==1">
                AND a.remark is not null and a.remark !=''
            </if>
            <if test="remark ==0">
                AND ( a.remark is null or a.remark ='' )
            </if>
            <if test="id!= null">
                AND a.id= #{id}
            </if>
            <if test="userId != null">
                AND a.user_id = #{userId}
            </if>
            <if test="address != null and address!=''">
                AND LOWER(a.address) = LOWER(#{address})
            </if>
            <if test="username != null and username !=''">
                AND a.username = #{username}
            </if>
            <if test="noticeFlag != null ">
                AND a.notice_flag = #{noticeFlag}
            </if>
            <if test="updateBy != null and updateBy !=''">
                AND a.update_by = #{updateBy}
            </if>
            <if test="serialId != null and serialId !=''">
                AND a.serial_id like concat('%', #{serialId}, '%')
            </if>
            <if test="txId != null and txId !=''">
                AND a.tx_id like concat('%', #{txId}, '%')
            </if>
            <if test="status != null and status!=''">
                AND a.status = #{status}
            </if>
            <if test="coin != null and coin!=''">
                AND a.coin like concat('%',#{coin},'%')
            </if>
            <if test="isTest != null">
                AND u.is_test = #{isTest}
            </if>
            <if test="type != null and type!=''">
                AND a.type = #{type}
            </if>
            <if test="orderType != null and orderType!='' and orderType!='-1'">
                <choose>
                    <when test="orderType==2">
                        AND a.order_type = #{orderType}
                    </when>
                    <otherwise>
                        AND (a.order_type = #{orderType} or a.order_type is null)
                    </otherwise>
                </choose>
            </if>
            <if test="adminParentIds != null and adminParentIds!=''">
                AND find_in_set(#{adminParentIds},a.admin_parent_ids)
            </if>
            <if test="params!=null">
                <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                    AND a.create_time &gt;= #{params.beginTime}
                </if>
                <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                    AND a.create_time &lt;= #{params.endTime}
                </if>
                <if test="params.startOperateTime != null and params.startOperateTime != ''"><!-- 开始时间检索 -->
                    AND a.operate_time &gt;= #{params.startOperateTime}
                </if>
                <if test="params.endOperateTime != null and params.endOperateTime != ''"><!-- 结束时间检索 -->
                    AND a.operate_time &lt;= #{params.endOperateTime}
                </if>
                <if test="params.isTest != null and params.isTest != ''">
                    AND u.is_test = #{params.isTest}
                </if>
                <if test="params.isTest != null and params.isTest != ''">
                    AND u.is_test = #{params.isTest}
                </if>
                <if test="params.minMoney != null and params.minMoney != ''">
                    AND a.amount &gt;= #{params.minMoney}
                </if>
                <if test="params.maxMoney != null and params.maxMoney != ''">
                    AND a.amount &lt;= #{params.maxMoney}
                </if>
            </if>
        </where>
        GROUP BY a.serial_id
        <!-- 数据范围过滤
        ${params.dataScope}-->
    </select>
    <select id="sumtotal" resultType="java.util.Map" parameterType="com.ruoyi.bussiness.domain.TAppRecharge">
        select IFNULL(SUM(amount),0) AS amount
        from t_own_recharge a
        LEFT JOIN t_app_user u ON a.user_id = u.user_id
        where 1=1
        <if test="id!= null">
            AND a.id= #{id}
        </if>
        <if test="userId != null">
            AND a.user_id = #{userId}
        </if>
        <if test="address != null and address!=''">
            AND a.address = #{address}
        </if>

        <if test="username != null and username !=''">
            AND a.username = #{username}
        </if>

        <if test="updateBy != null and updateBy !=''">
            AND a.update_by = #{updateBy}
        </if>

        <if test="serialId != null and serialId !=''">
            AND a.serial_id like concat('%', #{serialId}, '%')
        </if>
        <if test="txId != null and txId !=''">
            AND a.tx_id like concat('%', #{txId}, '%')
        </if>
        <if test="status != null and status!=''">
            AND a.status = #{status}
        </if>
        <if test="type != null and type!=''">
            AND a.type = #{type}
        </if>
        <if test="params!=null">
            <if test="params.isTest != null and params.isTest != ''">
                AND u.is_test = #{params.isTest}
            </if>
            <if test="params.minMoney != null and params.minMoney != ''">
                AND a.amount &gt;= #{params.minMoney}
            </if>
            <if test="params.maxMoney != null and params.maxMoney != ''">
                AND a.amount &lt;= #{params.maxMoney}
            </if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                AND a.update_time &gt;= #{params.beginTime}
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                AND a.update_time &lt;= #{params.endTime}
            </if>
            <!--        <if test="params.list != null and params.list.size > 0">&lt;!&ndash; 结束时间检索 &ndash;&gt;-->
            <!--            AND a.admin_user_id  in-->
            <!--            <foreach collection="params.list" separator="," item="item" open="(" close=")">-->
            <!--                #{item.userId}-->
            <!--            </foreach>-->
            <!--        </if>-->
        </if>
        <!-- 数据范围过滤
        ${params.dataScope}-->
    </select>
    <select id="selectRechargeVoice" resultType="com.ruoyi.bussiness.domain.TAppRecharge">
        select * from t_own_recharge r
        where r.status=0
        <if test="parentId != null  and  parentId!= '' ">
            and find_in_set(#{parentId},r.admin_parent_ids)
        </if>
    </select>
    <select id="getAllRecharge" resultType="java.math.BigDecimal">
        SELECT
        IFNULL(SUM(t.u_amount),0)
        FROM
        t_own_recharge w
        LEFT JOIN t_app_user u ON w.user_id = u.user_id
        LEFT JOIN t_app_wallet_record t ON t.serial_id = w.serial_id
        WHERE
        u.is_test = 0
        AND w.`status` = 1
        <if test="type!=null">
            <choose>
                <when test="type == -1">
                    AND (t.`type` = 1 or  t.`type` = 3)
                </when>
                <otherwise>
                    AND t.`type` =  #{type}
                </otherwise>
            </choose>
        </if>
        <if test="parentId != null and parentId!=''">
            AND find_in_set(#{parentId},w.admin_parent_ids)
        </if>
    </select>

    <insert id="insertTAppRecharge" parameterType="TAppRecharge" useGeneratedKeys="true" keyProperty="id">
        insert into t_own_recharge
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="userId != null">user_id,</if>
            <if test="username != null">username,</if>
            <if test="amount != null">amount,</if>
            <if test="bonus != null">bonus,</if>
            <if test="status != null">status,</if>
            <if test="serialId != null and serialId != ''">serial_id,</if>
            <if test="txId != null">tx_id,</if>
            <if test="type != null and type != ''">type,</if>
            <if test="searchValue != null">search_value,</if>
            <if test="address != null">address,</if>
            <if test="appParentIds != null">app_parent_ids,</if>
            <if test="adminParentIds != null">admin_parent_ids,</if>
            <if test="coin != null">coin,</if>
            <if test="toAddress != null">to_address,</if>
            <if test="blockTime != null">block_time,</if>
            <if test="host != null">host,</if>
            <if test="realAmount != null">real_amount,</if>
            <if test="fileName != null">file_name,</if>
            <if test="rechargeRemark != null">recharge_remark,</if>
            <if test="noticeFlag != null">notice_flag,</if>
            <if test="orderType != null">order_type,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="userId != null">#{userId},</if>
            <if test="username != null">#{username},</if>
            <if test="amount != null">#{amount},</if>
            <if test="bonus != null">#{bonus},</if>
            <if test="status != null">#{status},</if>
            <if test="serialId != null and serialId != ''">#{serialId},</if>
            <if test="txId != null">#{txId},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="searchValue != null">#{searchValue},</if>
            <if test="address != null">#{address},</if>
            <if test="appParentIds != null">#{appParentIds},</if>
            <if test="adminParentIds != null">#{adminParentIds},</if>
            <if test="coin != null">#{coin},</if>
            <if test="toAddress != null">#{toAddress},</if>
            <if test="blockTime != null">#{blockTime},</if>
            <if test="host != null">#{host},</if>
            <if test="realAmount != null">#{realAmount},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="rechargeRemark != null">#{rechargeRemark},</if>
            <if test="noticeFlag != null">#{noticeFlag},</if>
            <if test="orderType != null">#{orderType},</if>
        </trim>
    </insert>

    <update id="updateTAppRecharge" parameterType="TAppRecharge">
        update t_own_recharge
        <trim prefix="SET" suffixOverrides=",">
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="username != null">username = #{username},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="bonus != null">bonus = #{bonus},</if>
            <if test="status != null">status = #{status},</if>
            <if test="serialId != null and serialId != ''">serial_id = #{serialId},</if>
            <if test="txId != null">tx_id = #{txId},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="searchValue != null">search_value = #{searchValue},</if>
            <if test="address != null">address = #{address},</if>
            <if test="appParentIds != null">app_parent_ids = #{appParentIds},</if>
            <if test="adminParentIds != null">admin_parent_ids = #{adminParentIds},</if>
            <if test="coin != null">coin = #{coin},</if>
            <if test="toAddress != null">to_address = #{toAddress},</if>
            <if test="blockTime != null">block_time = #{blockTime},</if>
            <if test="host != null">host = #{host},</if>
            <if test="realAmount != null">real_amount = #{realAmount},</if>
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="rechargeRemark != null">recharge_remark = #{rechargeRemark},</if>
            <if test="noticeFlag != null">notice_flag = #{noticeFlag},</if>
            <if test="orderType != null">order_type = #{orderType},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateStatus" parameterType="TAppRecharge">
        update t_own_recharge
        <set>
            <if test="status!= null">status = #{status},</if>
            update_time = sysdate(),
            update_by =#{updateBy},
            remark=#{remark},
            real_amount=#{realAmount},
            recharge_remark=#{rechargeRemark}
        </set>
        where id = #{id}
    </update>

    <delete id="deleteTAppRechargeById" parameterType="Long">
        delete from t_own_recharge where id = #{id}
    </delete>

    <delete id="deleteTAppRechargeByIds" parameterType="String">
        delete from t_own_recharge where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getWeekRecharge" resultType="java.util.Map">
        SELECT
            DATE_FORMAT(w.create_time,'%Y-%m-%d') as createTime,
            IFNULL(SUM(t.u_amount),0) as recharge
        FROM
            t_own_recharge w
        LEFT JOIN t_app_user u ON w.user_id = u.user_id
        LEFT JOIN t_app_wallet_record t ON t.serial_id = w.serial_id
        WHERE
            u.is_test = 0
            AND w.`status` = 1
            AND (w.order_type = 1 OR w.order_type IS NULL)
            <if test="parentId != null and parentId!=''">
                AND find_in_set(#{parentId},w.admin_parent_ids)
            </if>
            AND DATE_SUB(CURDATE(), INTERVAL 7 DAY)  <![CDATA[<]]> DATE(w.create_time)
            AND w.user_id IN (SELECT u1.user_id FROM t_app_user u1)
        GROUP BY createTime;
    </select>

    <select id="getWeekFailedRecharge" resultType="java.util.Map">
        SELECT
            DATE_FORMAT(w.create_time,'%Y-%m-%d') as createTime,
            IFNULL(SUM(w.amount),0) as recharge
        FROM
            t_own_recharge w
        LEFT JOIN t_app_user u ON w.user_id = u.user_id
        WHERE
            u.is_test = 0
            AND w.`status` = 2
            AND (w.order_type = 1 OR w.order_type IS NULL)
            <if test="parentId != null and parentId!=''">
                AND find_in_set(#{parentId},w.admin_parent_ids)
            </if>
            AND DATE_SUB(CURDATE(), INTERVAL 7 DAY)  <![CDATA[<]]> DATE(w.create_time)
            AND w.user_id IN (SELECT u1.user_id FROM t_app_user u1)
        GROUP BY createTime;
    </select>

    <!-- 用户历史成功充值（USDT/USDC）总额：状态=通过，币种usdt/usdc，订单类型充值(null或1) -->
    <select id="sumPassedRechargeUsdtUsdcByUser" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(w.amount), 0)
        FROM t_own_recharge w
        WHERE w.user_id = #{userId}
          AND w.status = 1
          AND (w.order_type = 1 OR w.order_type IS NULL)
          AND LOWER(w.coin) IN ('usdt','usdc')
    </select>

    <!-- 所有有过成功 USDT/USDC 充值的用户ID，用于批量重算 -->
    <select id="selectUserIdsWithPassedUsdtUsdcRecharge" resultType="long">
        SELECT DISTINCT w.user_id
        FROM t_own_recharge w
        WHERE w.status = 1
          AND (w.order_type = 1 OR w.order_type IS NULL)
          AND LOWER(w.coin) IN ('usdt','usdc')
    </select>

</mapper>
