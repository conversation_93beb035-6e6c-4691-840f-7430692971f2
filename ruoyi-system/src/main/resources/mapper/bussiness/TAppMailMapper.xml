<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.bussiness.mapper.TAppMailMapper">
    
    <resultMap type="com.ruoyi.bussiness.domain.TAppMail" id="TAppMailResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="title"    column="title"    />
        <result property="content"    column="content"    />
        <result property="type"    column="type"    />
        <result property="status"    column="status"    />
        <result property="opertorId"    column="opertor_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="searchValue"    column="search_value"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectTAppMailVo">
        select id, user_id, title, content, type, status, opertor_id, create_time, update_time, search_value, del_flag from t_app_mail
    </sql>

    <select id="selectTAppMailList" parameterType="TAppMail" resultMap="TAppMailResult">
        <include refid="selectTAppMailVo"/>
        <where>
            and del_flag = '0'
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="title != null  and title != ''"> and title like concat('%',#{title},'%') </if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="opertorId != null  and opertorId != ''"> and opertor_id = #{opertorId}</if>
            <if test="searchValue != null  and searchValue != ''"> and search_value = #{searchValue}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectTAppMailById" parameterType="Long" resultMap="TAppMailResult">
        <include refid="selectTAppMailVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTAppMail" parameterType="TAppMail" useGeneratedKeys="true" keyProperty="id">
        insert into t_app_mail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="content != null">content,</if>
            <if test="type != null">type,</if>
            <if test="status != null">status,</if>
            <if test="opertorId != null">opertor_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="searchValue != null">search_value,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="content != null">#{content},</if>
            <if test="type != null">#{type},</if>
            <if test="status != null">#{status},</if>
            <if test="opertorId != null">#{opertorId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="searchValue != null">#{searchValue},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateTAppMail" parameterType="TAppMail">
        update t_app_mail
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="content != null">content = #{content},</if>
            <if test="type != null">type = #{type},</if>
            <if test="status != null">status = #{status},</if>
            <if test="opertorId != null">opertor_id = #{opertorId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="searchValue != null">search_value = #{searchValue},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTAppMailById" parameterType="Long">
        update  t_app_mail set del_flag = '2' where id = #{id}
    </delete>

    <delete id="deleteTAppMailByIds" parameterType="String">
        update  t_app_mail set del_flag = '2' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>