<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.bussiness.mapper.TUserCoinMapper">

    <resultMap type="com.ruoyi.bussiness.domain.TUserCoin" id="TUserCoinResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="coin"    column="coin"    />
        <result property="icon"    column="icon"    />
    </resultMap>

    <select id="removeByUserId">
        delete from t_user_coin where user_id=#{userId}
    </select>

</mapper>