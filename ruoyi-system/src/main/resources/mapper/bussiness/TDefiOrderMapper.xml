<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.bussiness.mapper.DefiOrderMapper">
    
    <resultMap type="com.ruoyi.bussiness.domain.DefiOrder" id="TDefiOrderResult">
        <result property="id"    column="id"    />
        <result property="amount"    column="amount"    />
        <result property="totleAmount"    column="totle_amount"    />
        <result property="rate"    column="rate"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="searchValue"    column="search_value"    />
        <result property="userId"    column="user_id"    />
        <result property="adminParentIds"    column="admin_parent_ids"    />
    </resultMap>

    <sql id="selectTDefiOrderVo">
        select id, amount, totle_amount, rate, create_by, create_time, update_by, update_time, remark, search_value, user_id, admin_parent_ids from t_defi_order
    </sql>

    <select id="selectTDefiOrderList" parameterType="DefiOrder" resultMap="TDefiOrderResult">
        <include refid="selectTDefiOrderVo"/>
        <where>  
            <if test="amount != null "> and amount = #{amount}</if>
            <if test="totleAmount != null "> and totle_amount = #{totleAmount}</if>
            <if test="rate != null "> and rate = #{rate}</if>
            <if test="searchValue != null  and searchValue != ''"> and search_value = #{searchValue}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="adminParentIds != null  and adminParentIds != ''"> and admin_parent_ids = #{adminParentIds}</if>
        </where>
    </select>
    
    <select id="selectTDefiOrderById" parameterType="Long" resultMap="TDefiOrderResult">
        <include refid="selectTDefiOrderVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTDefiOrder" parameterType="DefiOrder" useGeneratedKeys="true" keyProperty="id">
        insert into t_defi_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="amount != null">amount,</if>
            <if test="totleAmount != null">totle_amount,</if>
            <if test="rate != null">rate,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="searchValue != null">search_value,</if>
            <if test="userId != null">user_id,</if>
            <if test="adminParentIds != null">admin_parent_ids,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="amount != null">#{amount},</if>
            <if test="totleAmount != null">#{totleAmount},</if>
            <if test="rate != null">#{rate},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="searchValue != null">#{searchValue},</if>
            <if test="userId != null">#{userId},</if>
            <if test="adminParentIds != null">#{adminParentIds},</if>
         </trim>
    </insert>

    <update id="updateTDefiOrder" parameterType="DefiOrder">
        update t_defi_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="amount != null">amount = #{amount},</if>
            <if test="totleAmount != null">totle_amount = #{totleAmount},</if>
            <if test="rate != null">rate = #{rate},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="searchValue != null">search_value = #{searchValue},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="adminParentIds != null">admin_parent_ids = #{adminParentIds},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTDefiOrderById" parameterType="Long">
        delete from t_defi_order where id = #{id}
    </delete>

    <delete id="deleteTDefiOrderByIds" parameterType="String">
        delete from t_defi_order where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>