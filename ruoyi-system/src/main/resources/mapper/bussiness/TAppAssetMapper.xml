<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.bussiness.mapper.TAppAssetMapper">
    
    <resultMap type="com.ruoyi.bussiness.domain.TAppAsset" id="TAppAssetResult">
        <result property="userId"    column="user_id"    />
        <result property="adress"    column="adress"    />
        <result property="symbol"    column="symbol"    />
        <result property="amout"    column="amout"    />
        <result property="occupiedAmount"    column="occupied_amount"    />
        <result property="availableAmount"    column="available_amount"    />
        <result property="type"    column="type"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="searchValue"    column="search_value"    />
    </resultMap>

    <sql id="selectTAppAssetVo">
        select user_id, adress, symbol, amout, occupied_amount, available_amount, type, create_by, create_time, update_by, update_time, remark, search_value from t_app_asset
    </sql>

    <select id="selectTAppAssetList" parameterType="TAppAsset" resultMap="TAppAssetResult">
        select t.user_id, t.adress, t.symbol, t.amout,
        t.occupied_amount, t.available_amount, t.type, t.create_by, t.create_time, t.update_by, t.update_time,
        t.remark, t.search_value
        from t_app_asset t
        left join  t_app_user u on t.user_id=u.user_id

        <where>
            <if test="adminParentIds != null and adminParentIds!=''">
                AND find_in_set(#{adminParentIds},u.admin_parent_ids)
            </if>
            <if test="userId != null"> and t.user_id = #{userId}</if>
            <if test="symbol != null"> and t.symbol = #{symbol}</if>
            <if test="adress != null  and adress != ''"> and t.adress = #{adress}</if>
            <if test="amout != null "> and t.amout = #{amout}</if>
            <if test="type != null "> and t.type = #{type}</if>
            <if test="occupiedAmount != null "> and t.occupied_amount = #{occupiedAmount}</if>
            <if test="availableAmount != null "> and t.available_amount = #{availableAmount}</if>
            <if test="searchValue != null  and searchValue != ''"> and t.search_value = #{searchValue}</if>
            <if test="params!=null and params!=''">
                <if test="params.amountMin!=null">
                    and t.amout <![CDATA[ >= ]]> #{params.amountMin}
                </if>
                <if test="params.amountMax!=null">
                    and t.amout <![CDATA[ <= ]]> #{params.amountMax}
                </if>
                <if test="params.occupiedAmountMin!=null">
                    and t.occupied_amount <![CDATA[ >= ]]> #{params.occupiedAmountMin}
                </if>
                <if test="params.occupiedAmountMax!=null">
                    and t.occupied_amount <![CDATA[ <= ]]> #{params.occupiedAmountMax}
                </if>
                <if test="params.availableAmountMin!=null">
                    and t.available_amount <![CDATA[ >= ]]> #{params.availableAmountMin}
                </if>
                <if test="params.availableAmountMax!=null">
                    and t.available_amount <![CDATA[ <= ]]> #{params.availableAmountMax}
                </if>
                <if test="params.startTime!=null">
                    and t.create_time <![CDATA[ >= ]]> #{params.startTime}
                </if>
                <if test="params.endTime!=null">
                    and t.create_time <![CDATA[ <= ]]> #{params.endTime}
                </if>
            </if>
        </where>
        order by t.create_time desc
    </select>
    
    <select id="selectTAppAssetByUserId" parameterType="Long" resultMap="TAppAssetResult">
        <include refid="selectTAppAssetVo"/>
        where user_id = #{userId}
    </select>
        
    <insert id="insertTAppAsset" parameterType="TAppAsset">
        insert into t_app_asset
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="adress != null">adress,</if>
            <if test="symbol != null">symbol,</if>
            <if test="amout != null">amout,</if>
            <if test="occupiedAmount != null">occupied_amount,</if>
            <if test="availableAmount != null">available_amount,</if>
            <if test="type != null">type,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="searchValue != null">search_value,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="adress != null">#{adress},</if>
            <if test="symbol != null">#{symbol},</if>
            <if test="amout != null">#{amout},</if>
            <if test="occupiedAmount != null">#{occupiedAmount},</if>
            <if test="availableAmount != null">#{availableAmount},</if>
            <if test="type != null">#{type},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="searchValue != null">#{searchValue},</if>
         </trim>
    </insert>

    <update id="updateTAppAsset" parameterType="TAppAsset">
        update t_app_asset
        <trim prefix="SET" suffixOverrides=",">
            <if test="adress != null">adress = #{adress},</if>
            <if test="amout != null">amout = #{amout},</if>
            <if test="occupiedAmount != null">occupied_amount = #{occupiedAmount},</if>
            <if test="availableAmount != null">available_amount = #{availableAmount},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="searchValue != null">search_value = #{searchValue},</if>
        </trim>
        where user_id = #{userId}  and symbol =#{symbol}  and type=#{type}
    </update>
    <update id="updateByUserId" parameterType="com.ruoyi.bussiness.domain.TAppAsset">
        update t_app_asset
        <set>
            <if test="amout != null ">
                amout = #{amout},
            </if>
            <if test="occupiedAmount != null ">
                occupied_amount=#{occupiedAmount},
            </if>
            <if test="availableAmount != null ">
                available_amount=#{availableAmount},
            </if>
            update_time = now()
        </set>
        where user_id = #{userId} and symbol =#{symbol} and type=#{type}
    </update>

    <delete id="deleteTAppAssetByUserId" parameterType="Long">
        delete from t_app_asset where user_id = #{userId}
    </delete>

    <delete id="deleteTAppAssetByUserIds" parameterType="String">
        delete from t_app_asset where user_id in 
        <foreach item="userId" collection="array" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>

    <update id="noSettlementAssetByUserId" parameterType="java.util.Map">
        update t_app_asset
        <set>
            available_amount = available_amount - #{money},
            occupied_amount= occupied_amount + #{money},
            update_time = now()
        </set>
        where user_id = #{userId} and symbol =#{symbol}  and type=#{type}
    </update>

    <update id="reduceAssetByUserId" parameterType="java.util.Map">
        update t_app_asset
        <set>
            amout = amout - #{money},
            available_amount=available_amount-#{money},
            update_time = now()
        </set>
        where user_id = #{userId} and symbol =#{symbol} and type=#{type}
    </update>

    <update id="addAssetByUserId" parameterType="java.util.Map">
        update t_app_asset
        <set>
            amout = amout + #{money},
            available_amount=available_amount + #{money},
            update_time = now()
        </set>
        where user_id = #{userId} and symbol =#{symbol} and type=#{type}
    </update>

    <update id="occupiedAssetByUserId" parameterType="java.util.Map">
        update t_app_asset
        <set>
            available_amount = available_amount - #{money},
            occupied_amount= occupied_amount + #{money},
            update_time = now()
        </set>
        where user_id = #{userId} and symbol =#{symbol}  and type=#{type}
    </update>
    <update id="settlementOccupiedCurrencyOrder"
            parameterType="java.util.Map">
        update t_app_asset
        <set>
            amout = amout - #{money},
            occupied_amount= occupied_amount - #{subtract},
            available_amount= available_amount +#{availableAmount},
            update_time = now()
        </set>
        where user_id = #{userId} and symbol =#{symbol}  and type=#{type}
    </update>

</mapper>