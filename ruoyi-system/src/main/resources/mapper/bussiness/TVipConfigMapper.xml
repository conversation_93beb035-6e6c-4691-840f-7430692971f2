<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.bussiness.mapper.TVipConfigMapper">

    <resultMap id="TVipConfigResult" type="com.ruoyi.bussiness.domain.TVipConfig">
        <result property="id" column="id"/>
        <result property="level" column="level"/>
        <result property="levelName" column="level_name"/>
        <result property="requiredRange" column="required_range"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectTVipConfigVo">
        select id, level, level_name, required_range, create_by, create_time, update_by, update_time, remark from t_vip_config
    </sql>

    <select id="selectTVipConfigList" parameterType="com.ruoyi.bussiness.domain.TVipConfig" resultMap="TVipConfigResult">
        <include refid="selectTVipConfigVo"/>
        <where>
            <if test="level != null"> and level = #{level} </if>
            <if test="levelName != null and levelName != ''"> and level_name like concat('%', #{levelName}, '%') </if>
        </where>
        order by level asc
    </select>

    <select id="selectTVipConfigById" parameterType="long" resultMap="TVipConfigResult">
        <include refid="selectTVipConfigVo"/>
        where id = #{id}
    </select>

    <insert id="insertTVipConfig" parameterType="com.ruoyi.bussiness.domain.TVipConfig" useGeneratedKeys="true" keyProperty="id">
        insert into t_vip_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="level != null">level,</if>
            <if test="levelName != null">level_name,</if>
            <if test="requiredRange != null">required_range,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="level != null">#{level},</if>
            <if test="levelName != null">#{levelName},</if>
            <if test="requiredRange != null">#{requiredRange},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateTVipConfig" parameterType="com.ruoyi.bussiness.domain.TVipConfig">
        update t_vip_config
        <trim prefix="set" suffixOverrides=",">
            <if test="level != null">level = #{level},</if>
            <if test="levelName != null">level_name = #{levelName},</if>
            <if test="requiredRange != null">required_range = #{requiredRange},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTVipConfigById" parameterType="long">
        delete from t_vip_config where id = #{id}
    </delete>

    <delete id="deleteTVipConfigByIds" parameterType="long">
        delete from t_vip_config where id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!-- 根据累计金额匹配最高符合的等级 -->
    <select id="selectMaxLevelByAmount" resultMap="TVipConfigResult">
        <include refid="selectTVipConfigVo"/>
        where required_range &lt;= #{amount}
        order by level desc
        limit 1
    </select>

    <select id="selectByLevel" resultMap="TVipConfigResult">
        <include refid="selectTVipConfigVo"/>
        where level = #{level}
        limit 1
    </select>

</mapper>



